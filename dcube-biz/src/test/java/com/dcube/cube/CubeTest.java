/*
 * Copyright 2014 Ran <PERSON>g
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *         http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dcube.cube;

import com.dcube.cube.core.FactTable;
import com.dcube.cube.math.DoubleDouble;
import com.dcube.cube.spi.CubeServer;
import lombok.extern.slf4j.Slf4j;
import org.junit.BeforeClass;
import org.junit.Test;
import org.springframework.core.io.ClassPathResource;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class CubeTest {

    private static CubeServer cubeServer;

    @BeforeClass
    public static void prepare() throws Throwable {
        String dataFile = System.getProperty("dataFile", "cube.data");

        FactTable.FactTableBuilder builder = new FactTable.FactTableBuilder().build("cube.test")
                .addDimColumns(Arrays.asList("area", "product"))
                .addIndColumns(Arrays.asList("amount", "term", "interest"));

        long startTime = System.currentTimeMillis();
        log.info("prepare {} - start: {}", dataFile, startTime);
        ClassPathResource resource = new ClassPathResource(dataFile);
        BufferedReader reader = new BufferedReader(new InputStreamReader(resource.getInputStream()));
        String line = null;
        Integer index = 0;
        while ((line = reader.readLine()) != null) {
            String[] split = line.split("\t");
            index++;
            if (index % 100000 == 0) {
                log.debug("Load {} records", index);
            }
            builder.addDimDatas(index, Arrays.asList(String.valueOf(split[3]), String.valueOf(split[4])));
            builder.addIndDatas(index, Arrays.asList(new DoubleDouble(split[0]), new DoubleDouble(split[1]), new DoubleDouble(split[2])));
        }
        reader.close();
        log.info("prepare - end: {}, {}ms", System.currentTimeMillis(), System.currentTimeMillis() - startTime);

        cubeServer = new CubeServer(builder.done());
    }

    @Test
    public void test_0_1_group_sum() throws Throwable {
        Map<String, List<String>> filter = new HashMap<String, List<String>>(1);
        filter.put("product", Arrays.asList("产品A", "产品B"));
        cubeServer.setParallelMode(true);
        log.info(cubeServer.sum("amount", "product", null).toString());
    }

    @Test
    public void test_0_2_group_sum() throws Throwable {
        Map<String, List<String>> filter = new HashMap<String, List<String>>(1);
        filter.put("product", Arrays.asList("产品A", "产品B"));
        cubeServer.setParallelMode(true);
        List<String> indNameList = Arrays.asList("interest", "amount");
        List<String> groupByDimNameList = Arrays.asList("product", "area");
        cubeServer.multiSum(indNameList, groupByDimNameList, filter);
    }
}
