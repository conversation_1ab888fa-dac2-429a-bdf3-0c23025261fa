package com.dcube.tran.exception;

import com.dcube.common.exception.ServiceException;

import java.io.PrintWriter;
import java.io.StringWriter;

public class DataTranException extends ServiceException {

    private static final long serialVersionUID = 1L;

    private final ErrorCode errorCode;

    public DataTranException(ErrorCode errorCode, String errorMessage) {
        super(errorCode.toString() + " - " + errorMessage);
        this.errorCode = errorCode;
    }

    private DataTranException(ErrorCode errorCode, String errorMessage, Throwable cause) {
        super(errorCode.toString() + " - " + getMessage(errorMessage) + " - " + getMessage(cause), cause);
        this.errorCode = errorCode;
    }

    public static DataTranException asDataTranException(ErrorCode errorCode, String message) {
        return new DataTranException(errorCode, message);
    }

    public static DataTranException asDataTranException(ErrorCode errorCode, String message, Throwable cause) {
        if (cause instanceof DataTranException) {
            return (DataTranException) cause;
        }
        return new DataTranException(errorCode, message, cause);
    }

    public static DataTranException asDataTranException(ErrorCode errorCode, Throwable cause) {
        if (cause instanceof DataTranException) {
            return (DataTranException) cause;
        }
        return new DataTranException(errorCode, getMessage(cause), cause);
    }

    public ErrorCode getErrorCode() {
        return this.errorCode;
    }

    private static String getMessage(Object obj) {
        if (obj == null) {
            return "";
        }
        if (obj instanceof Throwable) {
            StringWriter str = new StringWriter();
            PrintWriter pw = new PrintWriter(str);
            ((Throwable) obj).printStackTrace(pw);
            return str.toString();
        } else {
            return obj.toString();
        }
    }
}
