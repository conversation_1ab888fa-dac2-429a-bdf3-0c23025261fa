package com.dcube.tran.exchanger;

import cn.hutool.core.collection.CollUtil;
import com.dcube.tran.channel.Channel;
import com.dcube.tran.element.Record;
import com.dcube.tran.element.TerminateRecord;
import com.dcube.tran.exception.CommonErrorCode;
import com.dcube.tran.exception.DataTranException;
import com.dcube.tran.execution.AbstractTranExecution;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Data
public class RecordExchanger implements RecordSender, RecordReceiver {

    private volatile boolean shutdown = false;

    private Channel channel;

    private List<AbstractTranExecution> tranExecutionList;

    public RecordExchanger(final Channel channel, List<AbstractTranExecution> tranExecutionList) {
        assert channel != null;
        this.channel = channel;
        this.tranExecutionList = tranExecutionList;
    }

    @Override
    public void sendToWriter(Record record) {
        if (shutdown) {
            throw DataTranException.asDataTranException(CommonErrorCode.RUNTIME_ERROR, "RecordExchanger is shutdown.");
        }
        if (CollectionUtils.isNotEmpty(tranExecutionList)) {
            List<Record> resultRecordList = new ArrayList<>();
            for (AbstractTranExecution tranExecution : tranExecutionList) {
                List<Record> tranRecordList = tranExecution.doTran(record);
                if (CollUtil.isNotEmpty(tranRecordList)) {
                    resultRecordList.addAll(tranRecordList);
                } else {
                    break;
                }
            }
            if (CollUtil.isNotEmpty(resultRecordList)) {
                this.channel.pushAll(resultRecordList);
            }
        } else {
            this.channel.push(record);
        }
    }

    @Override
    public boolean sendToWriter(Record record, long seconds) {
        if (shutdown) {
            throw DataTranException.asDataTranException(CommonErrorCode.RUNTIME_ERROR, "RecordExchanger is shutdown.");
        }
        return this.channel.push(record, seconds);
    }

    @Override
    public Record getFromReader() {
        if (shutdown) {
            throw DataTranException.asDataTranException(CommonErrorCode.RUNTIME_ERROR, "RecordExchanger is shutdown.");
        }
        Record record = this.channel.pull();
        return (record instanceof TerminateRecord ? null : record);
    }

    @Override
    public Record getFromReader(long seconds) {
        if (shutdown) {
            throw DataTranException.asDataTranException(CommonErrorCode.RUNTIME_ERROR, "RecordExchanger is shutdown.");
        }
        Record record = this.channel.pull(seconds);
        return record;
    }

    @Override
    public void terminate() {
        if (shutdown) {
            throw DataTranException.asDataTranException(CommonErrorCode.RUNTIME_ERROR, "RecordExchanger is shutdown.");
        }
        this.channel.pushTerminate(TerminateRecord.get());
    }

    @Override
    public void shutdown() {
        shutdown = true;
    }
}
