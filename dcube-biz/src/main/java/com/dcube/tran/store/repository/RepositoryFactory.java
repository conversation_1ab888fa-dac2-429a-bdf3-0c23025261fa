package com.dcube.tran.store.repository;

import com.dcube.biz.json.SourceConfigJson;
import com.dcube.biz.util.JdbcUtils;
import com.dcube.common.exception.ServiceException;
import com.dcube.tran.backup.ClickHouseBackupRepository;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RepositoryFactory {

    public static AbstractRepository getRepository(String dbType, SourceConfigJson config, String tableName) {
        AbstractRepository repository;
        switch (dbType.toUpperCase()) {
            case JdbcUtils.DB_TYPE_ORACLE:
                repository = new OracleRepository(JdbcUtils.DB_TYPE_ORACLE, config, tableName);
                break;
            case JdbcUtils.DB_TYPE_DM:
                repository = new DmRepository(JdbcUtils.DB_TYPE_DM, config, tableName);
                break;
            case JdbcUtils.DB_TYPE_MYSQL:
            case JdbcUtils.EXCEL:
                repository = new MysqlRepository(JdbcUtils.DB_TYPE_MYSQL, config, tableName);
                break;
            case JdbcUtils.DB_TYPE_POSTGRESQL:
                repository = new PostgreSqlRepository(JdbcUtils.DB_TYPE_POSTGRESQL, config, tableName);
                break;
            case JdbcUtils.DB_TYPE_MEMORY:
                repository = new MemoryRepository(JdbcUtils.DB_TYPE_MEMORY, config, tableName);
                break;
            case JdbcUtils.DB_TYPE_CLICKHOUSE:
                repository = new ClickHouseRepository(JdbcUtils.DB_TYPE_CLICKHOUSE, config, tableName);
                break;
            default:
                throw new ServiceException(String.format("不支持的数据源类型[%s]", dbType));
        }
        return repository;
    }

    public static AbstractRepository getRepository(SourceConfigJson config, String tableName) {
        AbstractRepository repository;
        switch (config.getDatabaseProductName()) {
            case "Oracle":
                repository = new OracleRepository(JdbcUtils.DB_TYPE_ORACLE, config, tableName);
                break;
            case "DM DBMS":
                repository = new DmRepository(JdbcUtils.DB_TYPE_DM, config, tableName);
                break;
            case "MySQL":
                repository = new MysqlRepository(JdbcUtils.DB_TYPE_MYSQL, config, tableName);
                break;
            case "ClickHouse":
                repository = new ClickHouseRepository(JdbcUtils.DB_TYPE_CLICKHOUSE, config, tableName);
                break;
            case "PostgreSQL":
                repository = new PostgreSqlRepository(JdbcUtils.DB_TYPE_POSTGRESQL, config, tableName);
                break;
            default:
                throw new ServiceException(String.format("不支持的数据源类型[%s]", config.getDatabaseProductName()));
        }
        return repository;
    }
}
