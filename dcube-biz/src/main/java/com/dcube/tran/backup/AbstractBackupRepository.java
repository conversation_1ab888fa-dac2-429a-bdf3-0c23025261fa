package com.dcube.tran.backup;

import cn.hutool.core.util.StrUtil;
import com.dcube.biz.domain.TableBackupConfig;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.biz.util.JdbcUtils;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.StringUtils;
import com.dcube.tran.store.repository.AbstractRepository;
import com.dcube.tran.store.repository.RepositoryFactory;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
public abstract class AbstractBackupRepository {
    public static final String NULL = "null";
    public static final String NOT_NULL = "not null";
    public static final int NUMERIC_DEFAULT_SCALE = 8;

    @Getter
    private final SourceConfigJson config;
    @Getter
    private final String dbType;
    @Getter
    private String tableName;
    @Getter
    private final Map<String, TableBackupConfig> backupConfigMap;
    @Getter
    private final int tableLevel;
    @Getter
    private final AbstractRepository abstractRepository;
    /**
     * 表注释
     */
    @Getter
    private final String tableComment;

    protected AbstractBackupRepository(String dbType, SourceConfigJson config, String tableName, Map<String, TableBackupConfig> backupConfigMap, int tableLevel, String tableComment) {
        this.dbType = dbType;
        this.config = config;
        if (JdbcUtils.DB_TYPE_ORACLE.equals(dbType) || JdbcUtils.DB_TYPE_DM.equals(dbType)) {
            this.tableName = tableName.toUpperCase();
        } else {
            this.tableName = tableName.toLowerCase();
        }
        this.backupConfigMap = backupConfigMap;
        this.tableLevel = tableLevel;
        this.abstractRepository = RepositoryFactory.getRepository(dbType, config, tableName);
        this.tableComment = tableComment;
    }

    public abstract String getIntColumn();

    public abstract String getBigintColumn();

    public abstract String getStringColumn();

    public abstract String getNumericColumn();

    public abstract String getDatetimeColumn();

    public void dropTable() {
        abstractRepository.executeSql("drop table if exists " + getTableName());
    }

    public abstract void createTable();

    public abstract String genAddColumnSql(TableBackupConfig tableBackupConfig);

    /**
     * 校验表的字段及类型长度是否一致
     */
    public boolean checkTableColumn(List<String> sqlList) {
        boolean flag = true;
        if (StrUtil.isBlank(tableName)) {
            tableName = "%";
        }
        Connection conn = null;
        ResultSet tableResultSet = null;
        try {
            conn = JdbcUtils.getConnection(config);
            if (JdbcUtils.DB_TYPE_ORACLE.equals(dbType) || JdbcUtils.DB_TYPE_DM.equals(dbType)) {
                tableName = tableName.toUpperCase();
            } else {
                tableName = tableName.toLowerCase();
            }

            DatabaseMetaData dm = conn.getMetaData();
            // 获取表的字段信息
            try (ResultSet columns = dm.getColumns(null, null, tableName, null)) {
                while (columns.next()) {
                    String columnName = StringUtils.upperCase(columns.getString("COLUMN_NAME")); // 字段名
                    String columnType = columns.getString("TYPE_NAME"); // 数据类型
                    int columnSize = columns.getInt("COLUMN_SIZE"); // 长度
                    String remarks = columns.getString("REMARKS");
                    TableBackupConfig tableBackupConfig = MapUtils.getObject(backupConfigMap, columnName);
                    if (tableBackupConfig != null) {
                        // 设置字段存在
                        tableBackupConfig.setColumnExist(true);
                        // 判断字段类型是否一致，不一致需要重新建表
                        if (!checkColumnTypeName(sqlList, columnType, tableBackupConfig, columnSize, remarks)) {
                            flag = false;
                            if (sqlList == null) {
                                break;
                            }
                        }
                    }
                }
            }
            // 是否有新增字段
            for (Map.Entry<String, TableBackupConfig> entry : backupConfigMap.entrySet()) {
                TableBackupConfig tableBackupConfig = entry.getValue();
                if (!tableBackupConfig.isColumnExist()) {
                    flag = false;
                    if (sqlList == null) {
                        break;
                    } else {
                        String genAddColumnSql = genAddColumnSql(tableBackupConfig);
                        sqlList.addAll(Arrays.asList(genAddColumnSql.split("\n")));
                    }
                }
            }
        } catch (SQLException e) {
            log.error("数据库操作异常。", e);
            throw new ServiceException(e);
        } finally {
            JdbcUtils.close(conn, null, tableResultSet);
        }
        return flag;
    }

    public abstract boolean checkColumnTypeName(List<String> sqlList, String columnType, TableBackupConfig tableBackupConfig, int columnSize, String remarks);

    /**
     * 生成表注释
     */
    public abstract String genTableCommentSql();

}