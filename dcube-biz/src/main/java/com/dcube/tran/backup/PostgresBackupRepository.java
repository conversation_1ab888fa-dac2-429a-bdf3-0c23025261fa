package com.dcube.tran.backup;

import com.dcube.biz.constant.enums.TableBackupColumnTypeEnum;
import com.dcube.biz.domain.TableBackupConfig;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class PostgresBackupRepository extends AbstractBackupRepository {

    // 修改SQL格式以适配PostgreSQL
    private static final String COLUMN_SQL_FORMAT = "%s %s %s";
    private static final String ADD_COLUMN_SQL_FORMAT = "ALTER TABLE %s ADD COLUMN " + COLUMN_SQL_FORMAT;
    private static final String COMMENT_TABLE_SQL_FORMAT = "COMMENT ON TABLE %s IS '%s'";
    private static final String COMMENT_COLUMN_SQL_FORMAT = "COMMENT ON COLUMN %s.%s IS '%s'";
    private static final String MODIFY_COLUMN_SQL_FORMAT = "ALTER TABLE %s ALTER COLUMN %s TYPE %s";

    PostgresBackupRepository(String dbType, SourceConfigJson config, String tableName,
                             Map<String, TableBackupConfig> backupConfigMap,
                             int tableLevel, String tableComment) {
        super(dbType, config, tableName, backupConfigMap, tableLevel, tableComment);
    }

    // 修改数据类型映射
    @Override
    public String getIntColumn() {
        return "INTEGER"; // PostgreSQL不需要指定长度
    }

    @Override
    public String getBigintColumn() {
        return "BIGINT";
    }

    @Override
    public String getStringColumn() {
        return "VARCHAR(%s)";
    }

    @Override
    public String getNumericColumn() {
        return "NUMERIC(%s,%s)";
    }

    @Override
    public String getDatetimeColumn() {
        return "TIMESTAMP";
    }

    @Override
    public void createTable() {
        List<String> columnList = buildCreateTableColumns();
        // 使用双引号处理标识符，移除内联注释
        String sql = String.format("CREATE TABLE \"%s\" (%s)",
                getTableName(), String.join(",", columnList));
        super.getAbstractRepository().executeSql(sql);

        // 单独添加表注释
        String tableCommentSql = genTableCommentSql();
        super.getAbstractRepository().executeSql(tableCommentSql);

        // 为所有列单独添加注释
        addColumnComments();
    }

    private void addColumnComments() {
        Map<String, TableBackupConfig> backupConfigMap = getBackupConfigMap();
        for (TableBackupConfig config : backupConfigMap.values()) {
            String commentSql = genColumnCommentSql(config);
            super.getAbstractRepository().executeSql(commentSql);
        }

        // 为固定字段添加注释
        TableBackupConfig versionConfig = new TableBackupConfig();
        versionConfig.setBackupColumnName("backup_version_name");
        versionConfig.setRemark("备份版本名称");
        super.getAbstractRepository().executeSql(genColumnCommentSql(versionConfig));

        TableBackupConfig dateConfig = new TableBackupConfig();
        dateConfig.setBackupColumnName("base_data_dt");
        dateConfig.setRemark("数据日期");
        super.getAbstractRepository().executeSql(genColumnCommentSql(dateConfig));
    }

    private List<String> buildCreateTableColumns() {
        Map<String, TableBackupConfig> backupConfigMap = getBackupConfigMap();
        List<String> columns = new ArrayList<>(backupConfigMap.size());
        int tableLevel = getTableLevel();
        List<String> primaryKeyList = new ArrayList<>(tableLevel);
        int _isPrimaryKey = 0;

        for (Map.Entry<String, TableBackupConfig> entry : backupConfigMap.entrySet()) {
            boolean isPrimaryKey = _isPrimaryKey <= tableLevel;
            TableBackupConfig tableBackupConfig = entry.getValue();
            String columnName = tableBackupConfig.getBackupColumnName().toLowerCase();
            TableBackupColumnTypeEnum columnType = tableBackupConfig.getColumnType();

            // 构建列定义 (移除内联注释)
            switch (columnType) {
                case INTEGER:
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName,
                            String.format(getIntColumn(), tableBackupConfig.getColumnLength()),
                            isPrimaryKey ? NOT_NULL : NULL));
                    break;
                case BIGINT:
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName,
                            getBigintColumn(),
                            isPrimaryKey ? NOT_NULL : NULL));
                    break;
                case VARCHAR:
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName,
                            String.format(getStringColumn(), tableBackupConfig.getColumnLength()),
                            isPrimaryKey ? NOT_NULL : NULL));
                    break;
                case NUMERIC:
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName,
                            String.format(getNumericColumn(), tableBackupConfig.getColumnLength(), NUMERIC_DEFAULT_SCALE),
                            isPrimaryKey ? NOT_NULL : NULL));
                    break;
                case DATETIME:
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName,
                            getDatetimeColumn(),
                            isPrimaryKey ? NOT_NULL : NULL));
                    break;
                default:
                    log.error("Unsupported type: " + columnType);
                    break;
            }

            if (isPrimaryKey) {
                primaryKeyList.add(columnName);
                _isPrimaryKey++;
            }
        }

        // 添加固定字段
        columns.add(String.format(COLUMN_SQL_FORMAT, "backup_version_name",
                String.format(getStringColumn(), 128), NOT_NULL));
        columns.add(String.format(COLUMN_SQL_FORMAT, "base_data_dt",
                getDatetimeColumn(), NOT_NULL));

        // 添加主键约束
        primaryKeyList.add(0, "backup_version_name");
        primaryKeyList.add(1, "base_data_dt");
        columns.add(String.format("PRIMARY KEY (%s)", String.join(",", primaryKeyList)));

        return columns;
    }

    @Override
    public String genAddColumnSql(TableBackupConfig tableBackupConfig) {
        return genColumnSql(tableBackupConfig, ADD_COLUMN_SQL_FORMAT);
    }

    public String genModifyColumnSql(TableBackupConfig tableBackupConfig) {
        return genColumnSql(tableBackupConfig, MODIFY_COLUMN_SQL_FORMAT);
    }

    private String genColumnSql(TableBackupConfig tableBackupConfig, String format) {
        String sql = "";
        TableBackupColumnTypeEnum columnType = tableBackupConfig.getColumnType();
        String columnName = tableBackupConfig.getBackupColumnName().toLowerCase();

        switch (columnType) {
            case INTEGER:
                sql = String.format(format, getTableName(), columnName,
                        String.format(getIntColumn(), tableBackupConfig.getColumnLength()));
                break;
            case BIGINT:
                sql = String.format(format, getTableName(), columnName,
                        getBigintColumn());
                break;
            case VARCHAR:
                sql = String.format(format, getTableName(), columnName,
                        String.format(getStringColumn(), tableBackupConfig.getColumnLength()));
                break;
            case NUMERIC:
                sql = String.format(format, getTableName(), columnName,
                        String.format(getNumericColumn(), tableBackupConfig.getColumnLength(), NUMERIC_DEFAULT_SCALE));
                break;
            case DATETIME:
                sql = String.format(format, getTableName(), columnName,
                        getDatetimeColumn());
                break;
            default:
                log.error("Unsupported type: " + columnType);
                break;
        }
        return sql;
    }

    // 生成列注释SQL
    public String genColumnCommentSql(TableBackupConfig tableBackupConfig) {
        String remark = tableBackupConfig.getRemark();
        if (StringUtils.isEmpty(remark)) {
            remark = tableBackupConfig.getColumnName();
        }
        return String.format(COMMENT_COLUMN_SQL_FORMAT,
                getTableName(),
                tableBackupConfig.getBackupColumnName().toLowerCase(),
                remark);
    }

    @Override
    public boolean checkColumnTypeName(List<String> sqlList, String columnType,
                                       TableBackupConfig tableBackupConfig,
                                       int columnSize, String remarks) {
        boolean typeMatch = false;
        boolean lengthMatch = true;
        boolean commentMatch = false;

        // 设置默认备注
        if (StringUtils.isEmpty(tableBackupConfig.getRemark())) {
            tableBackupConfig.setRemark(tableBackupConfig.getColumnName());
        }

        // 类型检查
        switch (tableBackupConfig.getColumnType()) {
            case INTEGER:
                typeMatch = columnType.equalsIgnoreCase("INT4") ||
                        columnType.equalsIgnoreCase("INTEGER");
                break;
            case BIGINT:
                typeMatch = columnType.equalsIgnoreCase("INT8") ||
                        columnType.equalsIgnoreCase("BIGINT");
                break;
            case VARCHAR:
                typeMatch = columnType.equalsIgnoreCase("VARCHAR") ||
                        columnType.equalsIgnoreCase("CHARACTER VARYING");
                if (typeMatch) {
                    lengthMatch = checkColumnLength(tableBackupConfig, columnSize);
                }
                break;
            case NUMERIC:
                typeMatch = columnType.equalsIgnoreCase("NUMERIC") ||
                        columnType.equalsIgnoreCase("DECIMAL");
                if (typeMatch) {
                    lengthMatch = checkColumnLength(tableBackupConfig, columnSize);
                }
                break;
            case DATETIME:
                typeMatch = columnType.equalsIgnoreCase("TIMESTAMP") ||
                        columnType.equalsIgnoreCase("TIMESTAMPTZ");
                break;
        }

        // 注释检查
        commentMatch = StringUtils.equals(remarks, tableBackupConfig.getRemark());

        // 生成必要的SQL
        if (sqlList != null) {
            if (!typeMatch || !lengthMatch) {
                sqlList.add(genModifyColumnSql(tableBackupConfig));
            }
            if (!commentMatch) {
                sqlList.add(genColumnCommentSql(tableBackupConfig));
            }
        }

        return typeMatch && lengthMatch && commentMatch;
    }

    private boolean checkColumnLength(TableBackupConfig tableBackupConfig, int columnSize) {
        return tableBackupConfig.getColumnLength() == null ||
                columnSize == tableBackupConfig.getColumnLength();
    }

    @Override
    public String genTableCommentSql() {
        return String.format(COMMENT_TABLE_SQL_FORMAT, getTableName(), getTableComment());
    }
}