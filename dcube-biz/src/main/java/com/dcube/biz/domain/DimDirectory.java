package com.dcube.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

@Data
@TableName("cube_dim_directory")
public class DimDirectory extends Model<DimDirectory> {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer id;

    /**
     * 维度目录名称
     */
    private String dimDirectoryName;

    /**
     * 维度目录类型（分组或维度实例）
     */
    private String dimDirectoryType;

    /**
     * 维度类型（维度实例属性::主数据维度或一般维度）
     */
    private String dimType;

    /**
     * 显示序号
     */
    private Integer indexNo;

    /**
     * 上级目录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer parentId;

    /**
     * 成员数量
     */
    @TableField(exist = false)
    private Long itemSize;

    /**
     * 成员数量
     */
    @TableField(exist = false)
    private Integer orderNum;

}
