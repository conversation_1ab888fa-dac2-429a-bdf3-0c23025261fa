package com.dcube.biz.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@TableName("cube_2d_table")
public class Table extends Model<Table> {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 目录ID
     */
    private String directoryId;

    /**
     * 表格名称
     */
    private String tableName;

    /**
     * 建表方式
     */
    private String createMode;

    /**
     * 数据视图ID
     */
    private String viewId;

    /**
     * 表格元数据
     */
    private String tableMeta;

    /**
     * 子表标记
     */
    private String childFlag;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private Integer parentId;

    private String type;

    private String ancestors;

    private String memTableName;

    /**
     * 表层级
     */
    private Integer tableLevel;

    @TableField(exist = false)
    private Long totalSize = 0L;

    @TableField(exist = false)
    private String memorySize;

    @TableField(exist = false)
    @JsonIgnore
    @JSONField(serialize = false)
    private Long memorySizeLong;

    @TableField(exist = false)
    private String viewName;

    /**
     * 加载的数据视图ID
     */
    @Schema(description = "加载的数据视图ID")
    private String loadedViewId;

}
