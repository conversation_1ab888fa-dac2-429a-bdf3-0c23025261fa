package com.dcube.biz.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.domain.DimDimRel;
import com.dcube.biz.dto.DimDimRelDto;
import com.dcube.biz.mapper.DimDimRelMapper;
import com.dcube.biz.service.IDimDimRelService;
import com.dcube.common.exception.ServiceException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class DimDimRelServiceImpl extends ServiceImpl<DimDimRelMapper, DimDimRel> implements IDimDimRelService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateExt(DimDimRelDto dimRelDto) {
        if (dimRelDto.getTableId() == null || dimRelDto.getTableId() < 1) {
            log.error("维度表ID不能为空。");
            throw new ServiceException("维度表ID不能为空。");
        }
        if (dimRelDto.getResDimDirectoryId() == null || dimRelDto.getResDimDirectoryId() < 1) {
            log.error("源维度目录ID不能为空。");
            throw new ServiceException("源维度目录ID不能为空。");
        }
        if (dimRelDto.getResInstanceId() == null || dimRelDto.getResInstanceId() < 1) {
            log.error("源维度实例ID不能为空。");
            throw new ServiceException("源维度实例ID不能为空。");
        }
        if (dimRelDto.getTarDimDirectoryId() == null || dimRelDto.getTarDimDirectoryId() < 1) {
            log.error("目标维度目录ID不能为空。");
            throw new ServiceException("目标维度目录ID不能为空。");
        }

        // exclusive deletion rel
        this.remove(new QueryWrapper<DimDimRel>().lambda()
                .eq(DimDimRel::getTableId, dimRelDto.getTableId())
                .and(wrapper -> wrapper.ne(DimDimRel::getResDimDirectoryId, dimRelDto.getResDimDirectoryId())
                        .or().ne(DimDimRel::getTarDimDirectoryId, dimRelDto.getTarDimDirectoryId())));

        // delete rel
        this.remove(new QueryWrapper<DimDimRel>().lambda()
                .eq(DimDimRel::getTableId, dimRelDto.getTableId())
                .eq(DimDimRel::getResDimDirectoryId, dimRelDto.getResDimDirectoryId())
                .eq(DimDimRel::getResInstanceId, dimRelDto.getResInstanceId())
                .eq(DimDimRel::getTarDimDirectoryId, dimRelDto.getTarDimDirectoryId()));

        // save rel
        if (CollectionUtil.isNotEmpty(dimRelDto.getTarInstanceIdList())) {
            List<DimDimRel> relList = new ArrayList<>();
            for (Integer tarInstanceId : dimRelDto.getTarInstanceIdList()) {
                DimDimRel rel = new DimDimRel();
                rel.setTableId(dimRelDto.getTableId());
                rel.setResDimDirectoryId(dimRelDto.getResDimDirectoryId());
                rel.setResInstanceId(dimRelDto.getResInstanceId());
                rel.setTarDimDirectoryId(dimRelDto.getTarDimDirectoryId());
                rel.setTarInstanceId(tarInstanceId);
                relList.add(rel);
            }
            this.saveBatch(relList);
        }
    }
}
