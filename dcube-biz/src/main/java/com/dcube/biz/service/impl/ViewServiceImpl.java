package com.dcube.biz.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.EnumUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.cache.selector.SimpleReadCacheSelector;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.base.PageDto;
import com.dcube.biz.config.DataSourceConfig;
import com.dcube.biz.domain.Source;
import com.dcube.biz.domain.View;
import com.dcube.biz.dto.GroupInstanceDto;
import com.dcube.biz.dto.ViewDto;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.mapper.ViewMapper;
import com.dcube.biz.service.IGroupInstanceService;
import com.dcube.biz.service.ISourceService;
import com.dcube.biz.service.IViewService;
import com.dcube.biz.util.JdbcUtils;
import com.dcube.biz.util.MemGridUtils;
import com.dcube.biz.util.TableUtils;
import com.dcube.biz.vo.SimpleViewVo;
import com.dcube.biz.vo.ViewVo;
import com.dcube.common.config.DCubeConfig;
import com.dcube.common.constant.Constants;
import com.dcube.common.enums.FileTypeEnum;
import com.dcube.common.enums.ValueType;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.file.FileUploadUtils;
import com.dcube.common.utils.poi.Column;
import com.dcube.common.utils.poi.ExcelData;
import com.dcube.common.utils.poi.POIUtils;
import com.dcube.grid.TableMetaData;
import com.dcube.tran.store.repository.AbstractRepository;
import com.dcube.tran.store.repository.RepositoryFactory;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.sql.Connection;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.regex.Pattern;

@Slf4j
@Service
public class ViewServiceImpl extends ServiceImpl<ViewMapper, View> implements IViewService {

    private static final Pattern SELECT_PATTERN = Pattern.compile("^(?i)\\s*select\\s+");

    // 注释后拼接恶意代码
    //                    "|0x[0-9a-f]+" +                                  // 十六进制编码绕过
    //                    "|\\$\\$"+                                        // PostgreSQL美元符号
    private static final Pattern SQL_INJECTION_PATTERN = Pattern.compile(
            // 匹配危险操作（不区分大小写）
            "(?i)" +
                    "(" +
                    "\\b(update|delete|drop|exec|truncate)\\b" +  // 关键操作词
                    "|union\\s+all" +                             // UNION注入
                    "|;\\s*(?!-|/\\*)" +                          // 多语句分隔符（排除注释）
                    "|(set|into)\\s+@\\w+" +                      // 变量赋值（如`SET @x=...`)
//                    "|(from|join)\\s+[\\[\\]\\w]+\\s+(?i:as\\s+)?\\w+\\s*" + // 跨表别名绕过
                    "|\\b(pg_sleep|load_file|xp_cmdshell)\\b" +   // 危险函数
                    ")" +
                    "|\\*/\\s*@\\w+", Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    public static final Integer LIMIT = Constants.PREVIEW_COUNT;
    @Autowired
    private ISourceService sourceService;

    @Autowired
    private DataSourceConfig dataSourceConfig;

    @Autowired
    public IGroupInstanceService groupInstanceService;

    @Autowired
    @Qualifier("sqlParserThreadPool")
    private ExecutorService sqlParserThreadPool;

    private final Gson gson = new Gson();

    @Override
    public ViewVo get(String id) {
        View viewDomain = this.getById(id);
        Assert.notNull(viewDomain, "未查询到视图");
        ViewVo vo = new ViewVo();
        BeanUtils.copyProperties(viewDomain, vo);
        if (StringUtils.isNotBlank(viewDomain.getSourceId())) {
            Source sourceDomain = sourceService.getById(viewDomain.getSourceId());
            if (Objects.nonNull(sourceDomain)) {
                vo.setSourceType(sourceDomain.getSourceType());
                if (StringUtils.isNotBlank(sourceDomain.getSourceConfig())) {
//                    vo.setSourceConfigString(sourceDomain.getSourceConfig());
                    vo.setSourceConfig(gson.fromJson(sourceDomain.getSourceConfig(), SourceConfigJson.class));
                }
            }
        }
        if (StringUtils.isNotBlank(viewDomain.getViewMeta())) {
            vo.setViewMetaList(gson.fromJson(viewDomain.getViewMeta(), new TypeToken<List<TableMetaJson>>() {
            }.getType()));
        }
        return vo;
    }

    @Override
    @Transactional
    public Boolean saveOrUpdate(ViewDto dto) {
        View domain = new View();
        BeanUtils.copyProperties(dto, domain);
        // 验证名称
        long count = this.count(domain.getId(), domain.getViewName());
        if (count > 0) {
            log.error(String.format("数据视图名称[%s]已存在。", domain.getViewName()));
            throw new ServiceException(String.format("数据视图名称[%s]已存在。", domain.getViewName()));
        }
        //检查数据库连接
        Connection connection = null;
        try {
            if (StringUtils.isNotBlank(dto.getSourceId()) && dto.getSourceConfig() == null) {
                Source sourceDomain = sourceService.getById(dto.getSourceId());
                if (Objects.nonNull(sourceDomain)) {
                    dto.setSourceType(sourceDomain.getSourceType());
                    if (StringUtils.isNotBlank(sourceDomain.getSourceConfig())) {
                        dto.setSourceConfig(gson.fromJson(sourceDomain.getSourceConfig(), SourceConfigJson.class));
                    }
                }
            }
            connection = JdbcUtils.getConnection(dto.getSourceConfig(), false);
            boolean success = JdbcUtils.isConnected(connection);
            if (!success) {
                log.error("连接失败,请检查连接配置。");
                throw new ServiceException("连接失败,请检查连接配置。");
            }
            List<TableMetaJson> metaJsonList = JdbcUtils.loadSqlFieldList(dto);
//            if (CollectionUtils.isNotEmpty(dto.getViewMetaList())) {
//                Map<String, String> codeMappingMap = StreamUtils.toMap(dto.getViewMetaList(), TableMetaJson::getCode, tableMetaJson -> StringUtils.defaultIfEmpty(tableMetaJson.getCodeMapping(), ""));
//                metaJsonList.forEach(tableMetaJson -> tableMetaJson.setCodeMapping(MapUtils.getString(codeMappingMap, tableMetaJson.getCode())));
//            }
            domain.setViewMeta(gson.toJson(metaJsonList));
            if (StringUtils.isBlank(domain.getId())) {
                this.save(domain);
            } else {
                this.updateById(domain);
            }
            return true;
        } finally {
            JdbcUtils.close(connection);
        }

    }

    @Override
    public Boolean remove(String[] ids) {
        return this.removeByIds(Arrays.asList(ids));
    }

    public long count(String id, String name) {
        return this.count(new QueryWrapper<View>().lambda()
                .ne(StringUtils.isNotBlank(id), View::getId, id)
                .eq(View::getViewName, name));
    }

    @Override
    public Map<String, Object> loadViewData(ViewDto dto) {
        if (StringUtils.isEmpty(dto.getViewScript()) && StringUtils.isEmpty(dto.getId())) {
            log.error("参数不能为空。");
            throw new ServiceException("参数不能为空。");
        }
//        Assert.isTrue(StringUtils.isNotEmpty(dto.getViewScript()), "sql语句不能为空");
        checkSqlInvalid(dto.getViewScript());

        Map<String, Object> result = Maps.newHashMapWithExpectedSize(2);
//        if (StringUtils.isNotEmpty(dto.getId())) {
//            ViewVo vo = this.get(dto.getId());
//            BeanUtils.copyProperties(vo, dto);
//        }

        if (StringUtils.isNotBlank(dto.getSourceId()) && dto.getSourceConfig() == null) {
            Source sourceDomain = sourceService.getById(dto.getSourceId());
            if (Objects.nonNull(sourceDomain)) {
                dto.setSourceType(sourceDomain.getSourceType());
                if (StringUtils.isNotBlank(sourceDomain.getSourceConfig())) {
                    dto.setSourceConfig(gson.fromJson(sourceDomain.getSourceConfig(), SourceConfigJson.class));
                }
            }
        }
        long totalCount = 0;
        List<Map<String, Object>> data = null;
        if (Objects.nonNull(dto.getSourceConfig()) && StringUtils.isNotEmpty(dto.getSourceType())) {
            AbstractRepository resRepository = RepositoryFactory.getRepository(dto.getSourceType(), dto.getSourceConfig(), null);
            PageDto pageDto = new PageDto();
            pageDto.setCurrentPage(1);
            pageDto.setPageSize(LIMIT);
            totalCount = resRepository.executeCountSql("select count(0) from(" + dto.getViewScript() + ") tmp");
            data = resRepository.listPage(dto.getViewScript(), pageDto);
        }
        result.put("result", data);
        result.put("totalCount", totalCount);
        return result;
    }

    public void checkSqlInvalid(String viewScript) {
        Assert.isTrue(SELECT_PATTERN.matcher(viewScript).find(), "sql语句需要以select开头");
//        if (SQL_INJECTION_PATTERN.matcher(viewScript).find()) {
//            log.error("非法sql语句：{}", viewScript);
//            throw new ServiceException("检测到可疑SQL结构");
//        }
        // sql校验
        Statement statement;
        try {
            statement = CCJSqlParserUtil.parse(viewScript, sqlParserThreadPool, null);
        } catch (Exception e) {
            log.error("非法sql语句：{}", viewScript, e);
            throw new ServiceException("sql非法请重新输入");
        }
        if (!(statement instanceof Select)) {
            log.error("非法sql语句：{}", viewScript);
            throw new ServiceException("sql非法请重新输入");
        }
//        Select select = (Select) statement;
//        if (select instanceof PlainSelect) {
//            PlainSelect plainSelect = select.getPlainSelect();
//            List<Join> joins = plainSelect.getJoins();
//            if (CollectionUtils.isNotEmpty(joins)) {
//                log.error("非法sql语句：{}", viewScript);
//                throw new ServiceException("请勿在sql中进行join");
//            }
////            plainSelect.getWhere().accept(new ExpressionVisitorAdapter(){
////                @Override
////                public void visit(OrExpression expr) {
////                    expr.accept(new ExpressionVisitorAdapter() {
////
////                    });
////                }
////            });
//        } else if (select instanceof SetOperationList) {
//            SetOperationList setOperationList = select.getSetOperationList();
//            List<SetOperation> operations = setOperationList.getOperations();
//            if (CollectionUtils.isNotEmpty(operations)) {
//                log.error("非法sql语句：{}", viewScript);
//                throw new ServiceException("请勿在sql中进行union");
//            }
//        }
    }


    @Override
    public Map<String, Object> loadViewDataById(String id) {
        ViewVo vo = this.get(id);
        Assert.notNull(vo, "未查询到视图");
        ViewDto dto = new ViewDto();
        dto.setId(id);
        BeanUtils.copyProperties(vo, dto);
        return loadViewData(dto);
    }

    @Override
    public List<TableMetaJson> load(ViewDto dto) {
        if (StringUtils.isEmpty(dto.getViewScript()) && StringUtils.isEmpty(dto.getId())) {
            log.error("参数不能为空。");
            throw new ServiceException("参数不能为空。");
        }
        checkSqlInvalid(dto.getViewScript());
        if (StringUtils.isNotBlank(dto.getSourceId()) && dto.getSourceConfig() == null) {
            Source sourceDomain = sourceService.getById(dto.getSourceId());
            if (Objects.nonNull(sourceDomain)) {
                dto.setSourceType(sourceDomain.getSourceType());
                if (StringUtils.isNotBlank(sourceDomain.getSourceConfig())) {
                    dto.setSourceConfig(gson.fromJson(sourceDomain.getSourceConfig(), SourceConfigJson.class));
                }
            }
        }
        List<TableMetaJson> tableMetaJsons = JdbcUtils.loadSqlFieldList(dto);
        if (CollectionUtils.isNotEmpty(tableMetaJsons)) {
            List<GroupInstanceDto> groupInstants = groupInstanceService.queryByStorageTypes(new ValueType[]{ValueType.DATE, ValueType.VARCHAR, ValueType.INTEGER, ValueType.DOUBLE});
            if (CollectionUtil.isNotEmpty(groupInstants)) {
                for (TableMetaJson meta : tableMetaJsons) {
                    String storageType;
                    if (ValueType.NUMERIC.name().equals(meta.getNewColumnType()) || ValueType.DECIMAL.name().equals(meta.getNewColumnType()) || ValueType.NUMBER.name().equals(meta.getNewColumnType()) || ValueType.FLOAT.name().equals(meta.getNewColumnType())) {
                        storageType = ValueType.DOUBLE.name();
                    } else if (ValueType.BIGINT.name().equals(meta.getNewColumnType()) || ValueType.INT.name().equals(meta.getNewColumnType()) || ValueType.INTEGER.name().equals(meta.getNewColumnType()) || ValueType.TINYINT.name().equals(meta.getNewColumnType())) {
                        storageType = ValueType.INTEGER.name();
                    } else if (ValueType.DATE.name().equals(meta.getNewColumnType()) || ValueType.DATETIME.name().equals(meta.getNewColumnType())) {
                        storageType = ValueType.DATE.name();
                    } else {
                        ValueType valueType = ValueType.find(meta.getNewColumnType());
                        if (valueType != null) {
                            storageType = valueType.name();
                        } else {
                            log.debug("未匹配到类型：{}", meta.getNewColumnType());
                            storageType = ValueType.VARCHAR.name();
                        }
                    }
                    String finalStorageType = storageType;
                    Optional<GroupInstanceDto> groupInstance = groupInstants.stream().filter(o -> o.getStorageType().equals(finalStorageType)).findFirst();
                    if (groupInstance.isPresent()) {
                        if (storageType.equals(ValueType.DOUBLE.name())) {
                            Optional<GroupInstanceDto> decimalDataFormat = groupInstants.stream().filter(o -> o.getStorageType().equals(finalStorageType) && o.getDecimalPlaces().intValue() == 2 && StringUtils.isEmpty(o.getSuffixChar())).findFirst();
                            if (decimalDataFormat.isPresent()) {
                                meta.setDataFormatId(decimalDataFormat.get().getId());
                                meta.setDataFormat(decimalDataFormat.get());
                            }
                        } else {
                            meta.setDataFormatId(groupInstance.get().getId());
                            meta.setDataFormat(groupInstance.get());
                        }
                    }
                }
            }
        }

        return tableMetaJsons;
    }

    @Override
    public List<TableMetaJson> loadById(String id) {
        ViewVo view = this.get(id);
        ViewDto dto = new ViewDto();
        BeanUtils.copyProperties(view, dto);
        return load(dto);
    }

    @Override
    public Map<String, Object> upload(MultipartFile file) throws IOException {
        Map<String, Object> result = new HashMap<>();
        List<Object> allowTypes = EnumUtil.getFieldValues(FileTypeEnum.class, "suffix");
        String suffix = FileUtil.getSuffix(file.getOriginalFilename()).toUpperCase();
        if (!allowTypes.contains(suffix)) {
            log.error("上传的文件格式必须是{}", allowTypes);
            throw new ServiceException(String.format("上传的文件格式必须是%s", allowTypes));
        }
        String fileName = FileUploadUtils.upload(DCubeConfig.getUploadPath(), file);
        result.put("filePath", fileName);
        return result;
    }

    @Async
    ExcelData loadDataFromFile(String path, FileTypeEnum format, Integer limit) throws IOException {
        switch (format) {
            case XLS:
            case XLSX:
                return POIUtils.loadExcel(path, limit);
            case CSV:
//                return CSVParse.create(path).parse();
            default:
                throw new ServiceException("文件格式转换错误。");
        }
    }

    private ViewDto handleExcelData(ViewDto dto) {
        //新增时，必须上传Excel
        if (StringUtils.isEmpty(dto.getId()) && StringUtils.isEmpty(dto.getFilePath())) {
            throw new ServiceException("Excel文件路径不能为空。");
        } else {
            if (StringUtils.isNotEmpty(dto.getFilePath())) {
                try {
                    String tableName = TableUtils.getRandomName(Constants.TABLE_PREFIX, dto.getViewName().replaceAll(" ", ""), Constants.TABLE_RANDOM_LENGTH);
                    SourceConfigJson sourceConfigJson = new SourceConfigJson(dataSourceConfig.getUrl(), dataSourceConfig.getUsername(), dataSourceConfig.getPassword(), dataSourceConfig.getDriverClassName(), null);
                    AbstractRepository repository = RepositoryFactory.getRepository(sourceConfigJson, tableName);
                    List<TableMetaJson> tableMetaJson = new ArrayList<>();
                    List<String> cols = new ArrayList<>();
                    StringBuffer selectSql = new StringBuffer();
                    int no = 0;
                    for (Column col : dto.getColumns()) {
                        TableMetaJson tableMetadata = new TableMetaJson();
                        String columnName = col.getName().replaceAll("\\s", "");
                        String colName = TableUtils.getRandomColName(Constants.COL_NAME_PREFIX, columnName, Constants.COL_RANDOM_LENGTH);
                        tableMetadata.setNo(no + 1);
                        tableMetadata.setCode(colName);
                        tableMetadata.setName(columnName);
                        tableMetadata.setOldColumnType(col.getType().name());
                        tableMetadata.setNewColumnType(col.getType().name());
                        cols.add(colName);
                        tableMetaJson.add(tableMetadata);
                        selectSql.append(colName).append(" AS").append(" '").append(columnName).append("',");
                        no++;
                    }
                    dto.setViewMetaList(tableMetaJson);
                    dto.setSourceConfig(sourceConfigJson);
                    //编辑视图
                    if (StringUtils.isNotEmpty(dto.getId()) && StringUtils.isNotEmpty(dto.getViewScript())) {
                        //删除表
                        String oldTableName = JdbcUtils.getTableName(dto);
                        repository.executeSql(String.format("DROP TABLE %s", oldTableName));
                    }
                    if (selectSql.length() > 0) {
                        selectSql.deleteCharAt(selectSql.length() - 1);
                    }
                    dto.setViewScript(String.format("SELECT %s FROM %s", selectSql, tableName));
                    //生成表
                    TableMetaData tableMetaData = MemGridUtils.parseColumn(tableName, tableMetaJson);
                    repository.syncTable(repository.getCatalog(), tableMetaData);

                    SimpleReadCacheSelector simpleReadCacheSelector = new SimpleReadCacheSelector();
                    simpleReadCacheSelector.setMaxUseMapCacheSize(5L);
                    simpleReadCacheSelector.setMaxCacheActivateBatchCount(20);
                    //导入数据
                    ExcelReader excelReader = EasyExcel.read(dto.getFilePath()).readCacheSelector(simpleReadCacheSelector).build();
                    ReadSheet sheet = null;
                    excelReader.read(sheet);
                    excelReader.finish();
                    //删除Excel文件
                    FileUtil.del(dto.getFilePath());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return dto;
    }

    @Override
    public List<SimpleViewVo> queryList(String viewName, String status) {
        return getBaseMapper().queryList(viewName, status);
    }

    @Override
    public Source getSourceByViewId(String viewId) {
        View view = this.getById(viewId);
        if (Objects.nonNull(view)) {
            if (StringUtils.isNotEmpty(view.getSourceId())) {
                return sourceService.getById(view.getSourceId());
            }
        }
        return null;
    }

}
