package com.dcube.biz.controller;

import com.dcube.biz.dto.TableBackupVersionSaveDTO;
import com.dcube.biz.service.ITableBackupVersionService;
import com.dcube.common.annotation.Log;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.enums.BusinessType;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/2dTableBackupVersion")
@Tag(name = "DCUBE-二维表备份版本信息", description = "DCUBE-二维表备份版本信息")
public class TableBackupVersionController extends BaseController {

    @Autowired
    private ITableBackupVersionService tableBackupVersionService;

    @Operation(summary = "根据二维表id查询二维表备份版本信息")
    @GetMapping("/queryByTableId")
    public AjaxResult queryByTableId(@RequestParam("tableId") Long tableId) {
        return AjaxResult.success(tableBackupVersionService.queryByTableId(tableId));
    }

    @Operation(summary = "保存二维表备份版本信息")
    @Log(title = "保存二维表备份版本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult saveTableBackupVersion(@RequestBody TableBackupVersionSaveDTO tableBackupVersionSaveDto) {
        return tableBackupVersionService.saveTableBackupVersion(tableBackupVersionSaveDto);
    }

    @Operation(summary = "查询后缀枚举")
    @GetMapping("/getTableBackupSuffixType")
    public AjaxResult getTableBackupSuffixType() {
        return AjaxResult.success(tableBackupVersionService.getTableBackupSuffixType());
    }

}
