package com.dcube.biz.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcube.biz.constant.BizConstants;
import com.dcube.biz.domain.GroupInstance;
import com.dcube.biz.dto.GroupInstanceDto;
import com.dcube.biz.query.GroupInstanceListQuery;
import com.dcube.biz.service.IGroupInstanceService;
import com.dcube.common.annotation.Log;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.core.page.TableDataInfo;
import com.dcube.common.enums.BusinessType;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.poi.ExcelUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("group_instance")
@Tag(name = "DCUBE-分组实例", description = "DCUBE-分组实例")
public class GroupInstanceController extends BaseController {

    @Autowired
    private IGroupInstanceService groupInstanceService;

    /**
     * 查询分组实例树（全量）
     */
    //@PreAuthorize("@ss.hasPermi('dcube:group_instance:tree')")
    @GetMapping("tree")
    @Operation(summary = "查询分组实例树（全量）", description = "")
    public AjaxResult tree(GroupInstanceListQuery query) {
        return AjaxResult.success(groupInstanceService.tree(query, null));
    }

    @GetMapping("tree_exclude_dic")
    @Operation(summary = "查询分组实例树（排除字典分组）", description = "")
    public AjaxResult treeExcludeDic(GroupInstanceListQuery query) {
        return AjaxResult.success(groupInstanceService.tree(query, "字典"));
    }

    /**
     * 查询分组实例列表
     */
    //@PreAuthorize("@ss.hasPermi('dcube:group_instance:list')")
    @GetMapping("list")
    @Operation(summary = "查询分组实例列表", description = "分页参数pageNum和pageSize直接拼接到url上")
    public TableDataInfo list(GroupInstanceListQuery query) {
        startPage();
        QueryWrapper<GroupInstance> qw = new QueryWrapper<>();
        qw.lambda().eq(StringUtils.isNotEmpty(query.getGroupId()), GroupInstance::getGroupId, query.getGroupId());
        qw.lambda().like(StringUtils.isNotEmpty(query.getGroupInstanceName()), GroupInstance::getGroupInstanceName, query.getGroupInstanceName());
        qw.lambda().eq(StringUtils.isNotEmpty(query.getStatus()), GroupInstance::getStatus, query.getStatus());
        qw.lambda().orderByDesc(GroupInstance::getCreateTime);
        List<GroupInstance> list = groupInstanceService.list(qw);
        return getDataTable(list);
    }

    /**
     * 导出分组实例列表
     */
    //@PreAuthorize("@ss.hasPermi('dcube:group_instance:export')")
    @Log(title = "分组实例", businessType = BusinessType.EXPORT)
    @Operation(summary = "导出分组实例列表")
    @PostMapping("export")
    public void export(HttpServletResponse response, GroupInstanceListQuery query) {
        QueryWrapper<GroupInstance> qw = new QueryWrapper<>();
        qw.lambda().eq(StringUtils.isNotEmpty(query.getGroupId()), GroupInstance::getId, query.getGroupId());
        qw.lambda().like(StringUtils.isNotEmpty(query.getGroupInstanceName()), GroupInstance::getGroupInstanceName, query.getGroupInstanceName());
        qw.lambda().eq(StringUtils.isNotEmpty(query.getStatus()), GroupInstance::getStatus, query.getStatus());
        qw.lambda().orderByDesc(GroupInstance::getCreateTime);
        List<GroupInstance> list = groupInstanceService.list(qw);
        ExcelUtil<GroupInstance> util = new ExcelUtil<>(GroupInstance.class);
        util.exportExcel(response, list, "分组实例");
    }

    /**
     * 获取分组实例详细信息
     */
    //@PreAuthorize("@ss.hasPermi('dcube:group_instance:query')")
    @Operation(summary = "主键查询")
    @GetMapping("{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(groupInstanceService.getById(id));
    }

    /**
     * 新增分组实例
     */
    //@PreAuthorize("@ss.hasPermi('dcube:group_instance:add')")
    @Log(title = "新增分组实例", businessType = BusinessType.INSERT)
    @Operation(summary = "新增分组实例")
    @PostMapping("add")
    public AjaxResult add(@RequestBody GroupInstanceDto dto) {
        dto.setId(CharSequenceUtil.EMPTY);
        dto.setStatus(BizConstants.STATUS_ENABLE);
        return toAjax(groupInstanceService.saveOrUpdate(dto));
    }

    /**
     * 修改分组实例
     */
    //@PreAuthorize("@ss.hasPermi('dcube:group_instance:edit')")
    @Log(title = "修改分组实例", businessType = BusinessType.UPDATE)
    @Operation(summary = "主键修改")
    @PostMapping("put")
    public AjaxResult edit(@RequestBody GroupInstanceDto dto) {
        return toAjax(groupInstanceService.saveOrUpdate(dto));
    }

    /**
     * 删除分组实例
     */
    //@PreAuthorize("@ss.hasPermi('dcube:group_instance:remove')")
    @Log(title = "删除分组实例", businessType = BusinessType.DELETE)
    @Operation(summary = "主键数组删除")
    @PostMapping("remove/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(groupInstanceService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 启用
     */
    //@PreAuthorize("@ss.hasPermi('dcube:group_instance:enable')")
    @Log(title = "启用分组实例", businessType = BusinessType.UPDATE)
    @Operation(summary = "启用")
    @PostMapping("enable")
    public AjaxResult enable(@RequestParam("id") String id) {
        GroupInstance domain = groupInstanceService.getById(id);
        domain.setStatus(BizConstants.STATUS_ENABLE);
        return toAjax(groupInstanceService.updateById(domain));
    }

    /**
     * 禁用
     */
    //@PreAuthorize("@ss.hasPermi('dcube:group_instance:disable')")
    @Log(title = "禁用分组实例", businessType = BusinessType.UPDATE)
    @Operation(summary = "禁用")
    @PostMapping("disable")
    public AjaxResult disable(@RequestParam("id") String id) {
        GroupInstance domain = groupInstanceService.getById(id);
        domain.setStatus(BizConstants.STATUS_DISABLED);
        return toAjax(groupInstanceService.updateById(domain));
    }

    @GetMapping("option")
    @Operation(summary = "查询分组实例列表(启用状态，用于页面下拉列表)", description = "")
    public AjaxResult option(GroupInstanceListQuery query) {
        QueryWrapper<GroupInstance> qw = new QueryWrapper<>();
        qw.lambda().eq(StringUtils.isNotEmpty(query.getGroupId()), GroupInstance::getId, query.getGroupId());
        qw.lambda().like(StringUtils.isNotEmpty(query.getGroupInstanceName()), GroupInstance::getGroupInstanceName, query.getGroupInstanceName());
        qw.lambda().eq(GroupInstance::getStatus, BizConstants.STATUS_ENABLE);
        qw.lambda().orderByDesc(GroupInstance::getCreateTime);
        List<GroupInstance> list = groupInstanceService.list(qw);
        return AjaxResult.success(list);
    }

}
