package com.dcube.biz.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode
public class TableDataLoadConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 二维表Id
     */
    @Schema(description = "二维表Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tableId;

    /**
     * 数据视图Id
     */
    @Schema(description = "数据视图Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private String viewId;

    /**
     * 二维表列编码
     */
    @Schema(description = "二维表列编码")
    private String tableColumnCode;

    /**
     * 二维表列名称
     */
    @Schema(description = "二维表列名称")
    private String tableColumnName;

    /**
     * 数据视图列编码
     */
    @Schema(description = "数据视图列编码")
    private String viewColumnCode;

}
