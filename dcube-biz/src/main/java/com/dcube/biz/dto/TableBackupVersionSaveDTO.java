package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import com.dcube.common.constant.enums.TableBackupSuffixTypeEnum;
import com.dcube.common.constant.enums.YNEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class TableBackupVersionSaveDTO extends BaseDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 二维表Id
     */
    @Schema(description = "二维表Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tableId;

    /**
     * 版本名称
     */
    @Schema(description = "版本名称")
    private String versionName;

    @Schema(description = "数据日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date baseDataDt;

    /**
     * 后缀类型
     */
    @Schema(description = "后缀类型")
    private TableBackupSuffixTypeEnum suffixType;

    /**
     * 是否自动覆盖同名备份
     */
    @Schema(description = "是否自动覆盖同名备份")
    private YNEnum isOverwrite;

}
