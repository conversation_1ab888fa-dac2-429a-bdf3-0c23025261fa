package com.dcube.biz.dto;

import com.dcube.common.constant.enums.MoveTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

@Data
@EqualsAndHashCode
@ToString
public class DimInstanceMoveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "维度实例id")
    private Long id;

    @Schema(description = "移动方式")
    private MoveTypeEnum moveType;

}
