package com.dcube.grid;

import org.apache.calcite.schema.Schema;
import org.apache.calcite.schema.Table;
import org.apache.calcite.schema.impl.AbstractSchema;

import java.util.HashMap;
import java.util.Map;

public class MemorySchema extends AbstractSchema {

    private static final MemoryDatabase DATABASE = new MemoryDatabase();

    public static final Schema INSTANCE = new MemorySchema();

    private MemorySchema() {
    }

    @Override
    protected Map<String, Table> getTableMap() {
        Map<String, Table> tableMap = new HashMap<>();
        for (Map.Entry<String, TableMetaData> entry : DATABASE.getTableMetadataMap().entrySet()) {
            String tableName = entry.getKey();
            TableMetaData tableMetadata = entry.getValue();
            Object[][] tableData = DATABASE.getTableDataMap().get(tableName);
            MemoryTable table = new MemoryTable(tableMetadata, tableData);
            tableMap.put(tableName, table);
        }
        return tableMap;
    }

    public static MemoryDatabase getDatabase() {
        return DATABASE;
    }
}
