package com.dcube.ai.domain;


import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.dcube.ai.constants.enums.ModelUseSceneEnum;
import com.dcube.ai.constants.enums.QuestionTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("cube_ai_chat_log")
@EqualsAndHashCode(callSuper = true)
public class AIChatLog extends Model<AIChatLog> implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 对话id
     */
    @Schema(description = "对话id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long chatId;

    /**
     * 问题id
     */
    @Schema(description = "问题id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long questionId;

    /**
     * 场景类型
     */
    @Schema(description = "场景类型")
    private ModelUseSceneEnum sceneType;

    /**
     * 问题类型
     */
    @Schema(description = "问题类型")
    private QuestionTypeEnum questionType;

    /**
     * 问题顺序
     */
    @Schema(description = "问题顺序")
    private Integer questionOrder;
    /**
     * 模型配置
     */
    @Schema(description = "模型配置")
    private String modelConfig;

    /**
     * 提示词
     */
    @Schema(description = "提示词")
    private String prompt;

    /**
     * ai结果
     */
    @Schema(description = "ai结果")
    private String aiResult;

    /**
     * 是否是问题结果（0否 1是）
     */
    @Schema(description = "是否是问题结果（0否 1是）")
    private Integer isResult;

    /**
     * 是否成功（0否 1是）
     */
    @Schema(description = "是否成功（0否 1是）")
    private Integer isSuccess;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @JsonIgnore
    @JSONField(serialize = false)
    @TableLogic
    @TableField(updateStrategy = FieldStrategy.NOT_EMPTY)
    private String delFlag;


}

