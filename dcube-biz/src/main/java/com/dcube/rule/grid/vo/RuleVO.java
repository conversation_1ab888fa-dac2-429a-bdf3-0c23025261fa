package com.dcube.rule.grid.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class RuleVO {

    /**
     * 表id
     */
    private Integer tableId;

    /**
     * 列编码
     */
    private String columnCode;


//    public RuleVO setColumnCode(String columnCode) {
//        this.columnCode = StringUtils.lowerCase(columnCode);
//        return this;
//    }
}