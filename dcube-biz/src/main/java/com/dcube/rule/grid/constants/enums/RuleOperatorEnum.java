package com.dcube.rule.grid.constants.enums;

public enum RuleOperatorEnum {
    /**
     * 当前表
     */
    O(1),
    /**
     * 父表
     */
    P(2),
    /**
     * 子表
     */
    C(3),
    /**
     * 其他表
     */
    T(4);

    final int operaterCode;

    RuleOperatorEnum(int operaterCode) {
        this.operaterCode = operaterCode;
    }

    public int getOperaterCode() {
        return operaterCode;
    }

}
