package com.dcube.rule.grid.constants;

public interface RuleConstant {
    /**
     * redis缓存前缀
     */
    String CACHE_PREFIX = "RULE:";
    /**
     * redis缓存前缀
     */
    String CUBE_CACHE_PREFIX = "RULE:CUBE:";
    /**
     * 父表关联字段
     */
    String PARENT_ASSOCIATE_FIELD = "_PARENT_ASSOCIATE_FIELD_";
//    /**
//     * 父表关联序列号
//     */
//    String PARENT_ASSOCIATE_NO = "_PARENT_ASSOCIATE_NO_";
    /**
     * 关联子表
     */
    String CHILD_ASSOCIATE_TABLES = "_CHILD_ASSOCIATE_TABLES_";
    /**
     * 父表id
     */
    String P_TABLE_ID = "_P_TABLE_ID_";
    /**
     * 父表表名
     */
    String P_TABLE_NAME = "_P_TABLE_NAME_";
    /**
     * 父表内存表名
     */
    String P_MEM_TABLE_NAME = "_P_MEM_TABLE_NAME_";
    /**
     * 当前表内存表名
     */
    String CURRENT_MEM_TABLE_NAME = "_CURRENT_MEM_TABLE_NAME_";
    /**
     * 表名
     */
    String TABLE_NAME = "_TABLE_NAME_";
    /**
     * 当前表id
     */
    String CURRENT_TABLE_ID = "_CURRENT_TABLE_ID_";
    /**
     * 当前列编码
     */
    String CURRENT_COLUMN_CODE = "_CURRENT_COLUMN_CODE_";
    /**
     * 列名和编码映射
     */
    String COLUMN_NAME_CODE_MAP = "_COLUMN_NAME_CODE_MAP_";
    /**
     * 表METADATA
     */
    String TABLE_METADATA = "_TABLE_METADATA_";
    String TABLE_METADATA_JSON = "_TABLE_METADATA_JSON_";
    /**
     * 保存规则引用关系
     */
    String SAVE_RULE_REF = "_SAVE_RULE_REF_";
    /**
     * 规则图
     */
    String RULE_GRAPH = "RULE_GRAPH";
    /**
     * 数据改变后需要执行的任务
     */
//    String AFTER_DATA_CHANGE_EVENT_TASK = "_AFTER_DATA_CHANGE_EVENT_TASK_";
    /**
     * 当前列名称
     */
    String CURRENT_COLUMN_NAME = "_CURRENT_COLUMN_NAME_";
    /**
     * 初始化执行规则的当前表名
     */
    String INIT_LOCAL_CACHE = "_INIT_LOCAL_CACHE_";
    /**
     * 初始化执行规则
     */
    String INIT_TABLE_LOCAL_CACHE = "_INIT_TABLE_LOCAL_CACHE_";
    /**
     * 表序号列名映射
     */
    String COLUMN_INDEX_MAP = "_COLUMN_INDEX_MAP_";
    /**
     * 反转表序号列名映射
     */
    String REVERSE_COLUMN_INDEX_MAP = "_REVERSE_COLUMN_INDEX_MAP_";
    /**
     * 表序号规则映射
     */
    String COLUMN_INDEX_EXPRESS_MAP = "_COLUMN_INDEX_EXPRESS_MAP_";
    /**
     * 表序号规则映射
     */
    String COLUMN_INDEX_RULE_ID_MAP = "_COLUMN_INDEX_RULE_ID_MAP_";
    /**
     * 表序号规则映射
     */
    String RULE_ID = "_RULE_ID_";

    /**
     * 任务唯一主键
     */
    String TASK = "_TASK_";
    /**
     * 表数据
     */
    String TABLE_DATA = "_TABLE_DATA_";
    /**
     * 表数据
     */
    String TABLE_DATA_LIST = "_TABLE_DATA_LIST_";
    /**
     * 表的列名称编码映射
     */
    String TABLE_COLUMN_NAME_CODE_MAP = "_TABLE_COLUMN_NAME_CODE_MAP_";
    /**
     * 执行规则计算的任务唯一主键
     */
//    String EXECUTE_EXPRESS_TASK_ID = "_EXECUTE_EXPRESS_TASK_ID_";
    /**
     * 本表分摊缓存
     */
    String ALLOCA_O_COUNT = "_ALLOCA_O_COUNT_";
    /**
     * 本表分摊缓存
     */
    String ALLOCA_O_SUM = "_ALLOCA_O_SUM_";
    /**
     * 是否收集他表数据（vsum不收集）
     */
    String COLLECT_T_REF = "_COLLECT_T_REF_";
    /**
     * 是否收集他表数据（csum不收集）
     */
    String COLLECT_C_REF = "_COLLECT_C_REF_";
    /**
     * 他表聚合
     */
    String VSUM_T_SUM = "_VSUM_T_SUM_";

    /**
     * 他表计数
     */
    String COUNT_T = "_COUNT_T_";

    /**
     * 他表最大
     */
    String MAX_T = "_MAX_T_";

    /**
     * 他表最小
     */
    String MIN_T = "_MIN_T_";

    /**
     * 他表平均
     */
    String VAVG_T = "_VAVG_T_";

    /**
     * 他表加权平均
     */
    String VAVGW_T = "_VAVGW_T_";

    /**
     * 他表加权平均
     */
    String VAVGW_T_2 = "_VAVGW_T_2_";
    /**
     * 子表加权平均
     */
    String CAVGW_T_ = "_CAVGW_T_";
    /**
     * 子表聚合
     */
    String CSUM = "_CSUM_";
    /**
     * 子表平均
     */
    String CAVG = "_CAVG_";
    /**
     * 子表计数
     */
    String CCOUNT = "_CCOUNT_";
    /**
     * 子表最大
     */
    String CMAX = "_CMAX_";
    /**
     * 子表最小
     */
    String CMIN = "_CMIN_";
    /**
     * 规则
     */
    String RULE_EXPRESS = "_RULE_EXPRESS_";
    /**
     * VLOOKUP函数的他表参数是主键id
     */
    String VLOOKUP_T_ID_FLAG = "_VLOOKUP_T_ID_FLAG_";
    /**
     * VLOOKUP函数的他表参数是主键id
     */
    String VLOOKUP_T_ID_COLUMN_NAME = "_VLOOKUP_T_ID_COLUMN_NAME_";
    /**
     * 多维规则
     */
    String CUBE_RULE = "_CUBE_RULE_";
    /**
     * 多维记录
     */
    String CUBE_RECORD = "_CUBE_RECORD_";
    /**
     * 多维元数据
     */
    String CUBE_META = "_CUBE_META_";
    /**
     * 多维表格
     */
    String CUBE_TABLE = "_CUBE_TABLE_";
    /**
     * 规则类型
     */
    String RULE_TYPE = "_RULE_TYPE_";
    /**
     * 累乘缓存
     */
    String PRODUCT_CACHE = "_PRODUCT_CACHE_";
    /**
     * VLOOKUP函数的反转map缓存
     */
    String VLOOKUP_T_REVERSE_MAP = "_VLOOKUP_T_REVERSE_MAP_";

    /**
     * VLOOKUP缓存
     */
    String VLOOKUP_T = "_VLOOKUP_T_";
    /**
     * VLOOKUPS缓存
     */
    String VLOOKUPS_T = "_VLOOKUPS_T_";
}
