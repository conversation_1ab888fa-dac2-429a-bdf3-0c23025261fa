package com.dcube.rule.cube.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.dcube.rule.cube.constants.enums.IndicatorOperationTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 多维计算规则指标运算
 * @TableName cube_dim_rule_indicator_operation
 */
@TableName(value = "cube_dim_rule_indicator_operation")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString
public class DimRuleIndicatorOperation extends Model<DimRuleIndicatorOperation> implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 多维表ID
     */
    @Schema(description = "多维表ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dimTableId;

    /**
     * 多维表规则ID
     */
    @Schema(description = "多维表规则ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dimRuleId;

    /**
     * 维度ID
     */
    @Schema(description = "维度ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dimDirectoryId;

    /**
     * 维度名称
     */
    @Schema(description = "维度名称")
    private String dimDirectoryName;

    /**
     * 生效范围
     */
    @Schema(description = "生效范围")
    private String effectScope;

    /**
     * 指标ID
     */
    @Schema(description = "指标ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long indId;

    /**
     * 指标名称
     */
    @Schema(description = "指标名称")
    private String indName;

    /**
     * 规则表达式
     */
    @Schema(description = "规则表达式")
    private String ruleExpression;

    /**
     * 规则表达式（内部）
     */
    @Schema(description = "规则表达式（内部）")
    private String ruleExpressionInner;

    /**
     * 指标运算类型（1作用范围，2指标规则）
     */
    @Schema(description = "指标运算类型（1作用范围，2指标规则）")
    private IndicatorOperationTypeEnum indicatorOperationType;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @JsonIgnore
    @JSONField(serialize = false)
    @TableLogic
    @TableField(updateStrategy = FieldStrategy.NOT_EMPTY)
    private String delFlag;

    /**
     * 选中数量
     */
    @Schema(description = "选中数量")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer effectScopeSize;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}