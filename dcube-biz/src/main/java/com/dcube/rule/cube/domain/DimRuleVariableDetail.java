package com.dcube.rule.cube.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.dcube.biz.constant.enums.DimMatchTypeEnum;
import com.dcube.biz.constant.enums.DimTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 多维计算规则多维变量详情
 * @TableName cube_dim_rule_variable_detail
 */
@TableName(value = "cube_dim_rule_variable_detail")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString
public class DimRuleVariableDetail extends Model<DimRuleVariableDetail> implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 多维变量ID
     */
    @Schema(description = "多维变量ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dimRuleVariableId;

    /**
     * 维度类型（0维度，1指标）
     */
    @Schema(description = "维度类型（0维度，1指标）")
    private DimTypeEnum dimType;

    /**
     * 变量多维表ID
     */
    @Schema(description = "变量多维表ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dimRuleVariableTableId;

    /**
     * 维度ID
     */
    @Schema(description = "维度ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dimDirectoryId;

    /**
     * 生效范围
     */
    @Schema(description = "生效范围")
    private String effectScope;

    /**
     * 选中数量
     */
    @Schema(description = "选中数量")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer effectScopeSize;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序")
    private Integer orderNum;

    /**
     * 匹配方式
     */
    @Schema(description = "匹配方式")
    private DimMatchTypeEnum matchType;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @JsonIgnore
    @JSONField(serialize = false)
    @TableLogic
    @TableField(updateStrategy = FieldStrategy.NOT_EMPTY)
    private String delFlag;


}