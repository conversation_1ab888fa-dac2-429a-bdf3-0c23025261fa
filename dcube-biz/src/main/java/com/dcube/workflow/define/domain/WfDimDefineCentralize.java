package com.dcube.workflow.define.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 多维流程定义归口维度对象 cube_wf_dim_define_centralize
 *
 * @date 2024-09-08
 */
@Data
@TableName("cube_wf_dim_define_centralize")
@EqualsAndHashCode(callSuper = false)
@ToString
public class WfDimDefineCentralize extends Model<WfDimDefineCentralize> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 多维流程定义id
     */
    @Schema(description = "多维流程定义id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dimDefineDetailId;

    /**
     * 归口节点名称
     */
    @Schema(description = "归口节点名称")
    private String centralizeName;

    /**
     * 归口节点维度id
     */
    @Schema(description = "归口节点维度id")
    private String centralizeDimId;

    /**
     * 归口节点维度数量
     */
    @Schema(description = "归口节点维度数量")
    private Integer centralizeDimSize;

    /**
     * 审批人id
     */
    @Schema(description = "审批人id")
    private String userId;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @JsonIgnore
    @JSONField(serialize = false)
    @TableLogic
    @TableField(updateStrategy = FieldStrategy.NOT_EMPTY)
    private String delFlag;

    /**
     * 审批人名称
     */
    @Schema(description = "审批人名称")
    @TableField(exist = false)
    private String userName;

    /**
     * 审批人id
     */
    @Schema(description = "审批人id数组")
    @TableField(exist = false)
    private List<String> userIds;

    /**
     * 任务状态
     */
    @Schema(description = "任务状态")
    @TableField(exist = false)
    private String taskStateName;

    /**
     * 节点id
     */
    @Schema(description = "节点id")
    @TableField(exist = false)
    private String nodeId;

    public List<String> getUserIds() {
        return userIds == null ? Collections.emptyList() : userIds;
    }
}
