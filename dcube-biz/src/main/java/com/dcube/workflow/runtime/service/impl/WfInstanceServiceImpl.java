package com.dcube.workflow.runtime.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.constant.BizConstants;
import com.dcube.biz.domain.DimInstance;
import com.dcube.biz.domain.DimTable;
import com.dcube.biz.domain.Table;
import com.dcube.biz.dto.CellDto;
import com.dcube.biz.dto.RowDto;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.query.TableListQuery;
import com.dcube.biz.service.IDimInstanceService;
import com.dcube.biz.service.IDimTableService;
import com.dcube.biz.service.ITableService;
import com.dcube.biz.vo.MemTableDataVo;
import com.dcube.common.core.domain.entity.SysUser;
import com.dcube.common.core.domain.model.LoginUser;
import com.dcube.common.utils.SecurityUtils;
import com.dcube.common.utils.StreamUtils;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.spring.SpringUtils;
import com.dcube.rule.grid.service.IRuleService;
import com.dcube.system.service.ISysUserService;
import com.dcube.workflow.define.constants.enums.FlowNodeTypeEnums;
import com.dcube.workflow.define.domain.WfDefine;
import com.dcube.workflow.define.domain.WfDimDefineCentralize;
import com.dcube.workflow.define.flowchart.FlowChart;
import com.dcube.workflow.define.service.IWfDefineService;
import com.dcube.workflow.define.service.IWfDimDefineDetailService;
import com.dcube.workflow.define.utils.FlowChartUtils;
import com.dcube.workflow.define.vo.WfDimDefineDetailVO;
import com.dcube.workflow.runtime.constants.WfRtConstants;
import com.dcube.workflow.runtime.constants.enums.WfEventTypeEnums;
import com.dcube.workflow.runtime.constants.enums.WfInstanceStateEnums;
import com.dcube.workflow.runtime.constants.enums.WfInstanceTypeEnums;
import com.dcube.workflow.runtime.constants.enums.WfTaskStateEnums;
import com.dcube.workflow.runtime.domain.WfHistory;
import com.dcube.workflow.runtime.domain.WfInstance;
import com.dcube.workflow.runtime.domain.WfTask;
import com.dcube.workflow.runtime.domain.WfVariable;
import com.dcube.workflow.runtime.dto.WfInstanceStartDTO;
import com.dcube.workflow.runtime.exception.WfRuntimeException;
import com.dcube.workflow.runtime.listener.event.WfEvent;
import com.dcube.workflow.runtime.listener.event.WfEventSource;
import com.dcube.workflow.runtime.mapper.WfInstanceMapper;
import com.dcube.workflow.runtime.service.IWfHistoryService;
import com.dcube.workflow.runtime.service.IWfInstanceService;
import com.dcube.workflow.runtime.service.IWfTaskService;
import com.dcube.workflow.runtime.service.IWfVariableService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 流程运行实例Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Service
public class WfInstanceServiceImpl extends ServiceImpl<WfInstanceMapper, WfInstance> implements IWfInstanceService {
    @Autowired
    private WfInstanceMapper wfInstanceMapper;
    @Autowired
    private IWfDefineService wfDefineService;
    @Autowired
    private ITableService tableService;
    @Autowired
    private IWfTaskService wfTaskService;
    @Autowired
    private IRuleService ruleService;
    @Autowired
    private IDimTableService dimTableService;
    @Autowired
    private IWfDimDefineDetailService wfDimDefineDetailService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IDimInstanceService dimInstanceService;
    @Autowired
    private IWfHistoryService wfHistoryService;
    @Autowired
    private IWfVariableService wfVariableService;

    /**
     * 查询流程运行实例
     *
     * @param id 流程运行实例主键
     * @return 流程运行实例
     */
    @Override
    public WfInstance selectCubeWfInstanceById(Long id) {
        return wfInstanceMapper.selectCubeWfInstanceById(id);
    }

    /**
     * 查询流程运行实例列表
     *
     * @param wfInstance 流程运行实例
     * @return 流程运行实例
     */
    @Override
    public List<WfInstance> selectCubeWfInstanceList(WfInstance wfInstance) {
        return wfInstanceMapper.selectCubeWfInstanceList(wfInstance);
    }

    /**
     * 新增流程运行实例
     *
     * @param wfInstance 流程运行实例
     * @return 结果
     */
    @Override
    public int insertCubeWfInstance(WfInstance wfInstance) {
        return wfInstanceMapper.insertCubeWfInstance(wfInstance);
    }

    /**
     * 修改流程运行实例
     *
     * @param wfInstance 流程运行实例
     * @return 结果
     */
    @Override
    public int updateCubeWfInstance(WfInstance wfInstance) {
        return wfInstanceMapper.updateCubeWfInstance(wfInstance);
    }

    /**
     * 批量删除流程运行实例
     *
     * @param ids 需要删除的流程运行实例主键
     * @return 结果
     */
    @Override
    public int deleteCubeWfInstanceByIds(Long[] ids) {
        return wfInstanceMapper.deleteCubeWfInstanceByIds(ids);
    }

    /**
     * 删除流程运行实例信息
     *
     * @param id 流程运行实例主键
     * @return 结果
     */
    @Override
    public int deleteCubeWfInstanceById(Long id) {
        return wfInstanceMapper.deleteCubeWfInstanceById(id);
    }

    /**
     * 开启流程
     *
     * @param wfInstanceStartDto 开始流程
     * @return 流程运行实例
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public WfInstance startProcess(WfInstanceStartDTO wfInstanceStartDto) {
        switch (wfInstanceStartDto.getFlowType()) {
            case TABLE:
                return startTableProcess(wfInstanceStartDto);
            case CUBE:
                return startCubeProcess(wfInstanceStartDto);
            default:
                throw new WfRuntimeException("无效的流程类型");
        }
    }

    private WfInstance startTableProcess(WfInstanceStartDTO wfInstanceStartDto) {
        Integer tableId = wfInstanceStartDto.getTableId();
        WfDefine wfDefine = wfDefineService.getByTableId(tableId, true);
        if (wfDefine == null) {
            throw new WfRuntimeException("未查询到流程定义");
        }
        if (StringUtils.isEmpty(wfDefine.getFlowchartJson())) {
            throw new WfRuntimeException("请先设计流程图");
        }

        Table table = tableService.getById(tableId);
        if (table == null) {
            throw new WfRuntimeException("未查询到二维表");
        }

        FlowChart flowChart = JSON.parseObject(wfDefine.getFlowchartJson(), FlowChart.class);
        FlowChart.EdgeDTO currentEdge = FlowChartUtils.getEdgeById(flowChart, wfInstanceStartDto.getNextEdgeId());
        if (currentEdge == null) {
            throw new WfRuntimeException("未找到下一个节点连线");
        }
        FlowChart.NodeDTO nextNode = FlowChartUtils.getTargetNodeByEdgeId(flowChart, wfInstanceStartDto.getNextEdgeId());
        if (nextNode == null) {
            throw new WfRuntimeException("未找到下一个节点");
        }

        FlowChart.NodeDTO.PropertiesDTO nextNodeProperties = nextNode.getProperties();
        String unitColumnCode = nextNodeProperties.getUnitColumnCode();
        String tableMeta = table.getTableMeta();
        List<TableMetaJson> tableMetaJson = StringUtils.isEmpty(tableMeta) ? Collections.emptyList() : JSON.parseArray(table.getTableMeta(), TableMetaJson.class);
        int unitColumn = 0;
        boolean unitColumnFlag = false;
        String wfStateColumnCode = wfDefine.getFlowStateColumnCode();
        int wfStateColumn = 0;
        boolean wfStateColumnFlag = false;
        for (TableMetaJson tableMetaJsonItem : tableMetaJson) {
            if (StringUtils.equals(tableMetaJsonItem.getCode(), unitColumnCode)) {
                unitColumnFlag = true;
            }
            if (!unitColumnFlag) {
                unitColumn++;
            }
            if (StringUtils.equals(tableMetaJsonItem.getCode(), wfStateColumnCode)) {
                wfStateColumnFlag = true;
            }
            if (!wfStateColumnFlag) {
                wfStateColumn++;
            }
            if (unitColumnFlag && wfStateColumnFlag) {
                break;
            }
        }
        if (!wfStateColumnFlag) {
            throw new WfRuntimeException("未找到二维表流程状态列");
        }
        if (nextNode.getType() == FlowNodeTypeEnums.TASK) {
            if (!unitColumnFlag) {
                throw new WfRuntimeException("未找到二维表单位列");
            }
        }
        String dataId = wfInstanceStartDto.getDataId();
        TableListQuery tableListQuery = new TableListQuery();
        tableListQuery.setTableId(tableId);
        tableListQuery.setFilterDataId(dataId);
        tableListQuery.setCurrentPage(1);
        tableListQuery.setPageSize(dataId.split(",").length);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        PageInfo<MemTableDataVo> pageInfo = tableService.queryTableData(tableListQuery, loginUser);
        if (pageInfo == null
                || CollectionUtils.isEmpty(pageInfo.getList())
                || pageInfo.getList().get(0) == null
                || CollectionUtils.isEmpty(pageInfo.getList().get(0).getRows())) {
            throw new WfRuntimeException("未查询到二维表数据");
        }
        String unitColumnVal = "";
        MemTableDataVo memTableDataVo = pageInfo.getList().get(0);
        for (RowDto rowDto : memTableDataVo.getRows()) {
            if (rowDto == null || CollectionUtils.isEmpty(rowDto.getCells()) || rowDto.getCells().size() <= unitColumn || rowDto.getCells().get(unitColumn) == null) {
                throw new WfRuntimeException("未查询到二维表单位列数据");
            }
            List<CellDto> cells = rowDto.getCells();
            CellDto cellDto = cells.get(unitColumn);
            if (StringUtils.isEmpty(unitColumnVal) && cellDto.getOriginalValue() != null) {
                unitColumnVal = String.valueOf(cellDto.getOriginalValue());
            } else {
                if (!StringUtils.equals(unitColumnVal, String.valueOf(cellDto.getOriginalValue()))) {
                    throw new WfRuntimeException("不能提交不同单位的数据");
                }
            }
            CellDto wfStateCellDto = cells.get(wfStateColumn);
            if (cellDto.getOriginalValue() == null || !StringUtils.equals(String.valueOf(wfStateCellDto.getOriginalValue()), BizConstants.WfState.PRE_SUBMIT)) {
                throw new WfRuntimeException("只能提交状态是待提交的数据");
            }
            for (CellDto cell : cells) {
                if (cell.getOriginalValue() != null) {
                    cell.setCellValue(String.valueOf(cell.getOriginalValue()));
                }
            }
            wfStateCellDto.setCellValue(BizConstants.WfState.UNDER_APPROVAL);
        }

        WfInstance wfInstance = new WfInstance();
        wfInstance.setInstanceState(WfInstanceStateEnums.RUNNING);
        wfInstance.setDefineId(wfDefine.getId());
        wfInstance.setDefineName(wfDefine.getFlowName());
        wfInstance.setProcessStarter(loginUser.getUsername());
        wfInstance.setProcessStarterId(String.valueOf(loginUser.getUserId()));
        wfInstance.setTableId(wfDefine.getTableId());
        wfInstance.setTableName(table.getTableName());
        wfInstance.setNodeId(nextNode.getId());
        wfInstance.setNodeName(nextNode.getProperties().getText());
        wfInstance.setDataId(dataId);
        wfInstance.setStartTime(new Date());
        this.save(wfInstance);
        // 发布流程发起事件
        WfEventSource wfEventSource = new WfEventSource();
        BeanUtils.copyProperties(wfInstance, wfEventSource);
        wfEventSource.setEventType(WfEventTypeEnums.START_PROCESS);
        wfEventSource.setMemTableName(table.getMemTableName());
        wfEventSource.setTableChildFlag(table.getChildFlag());
        wfEventSource.setTableLevel(table.getTableLevel());
        wfEventSource.setRows(memTableDataVo.getRows());
        wfEventSource.setUserId(String.valueOf(SecurityUtils.getUserId()));
        wfEventSource.setUserName(SecurityUtils.getUsername());
        SpringUtils.publishEvent(new WfEvent(wfEventSource));

        // 创建发起人的任务并完成
        FlowChart.NodeDTO startNode = FlowChartUtils.getStartNode(flowChart);
        FlowChart.NodeDTO.PropertiesDTO startNodeProperties = startNode.getProperties();
        WfTask wfTask = new WfTask();
        wfTask.setDefineId(wfInstance.getDefineId());
        wfTask.setDefineName(wfInstance.getDefineName());
        wfTask.setInstanceId(wfInstance.getId());
        wfTask.setTableId(wfInstance.getTableId());
        wfTask.setTableName(wfInstance.getTableName());
        wfTask.setNodeId(startNode.getId());
        wfTask.setNodeName(startNodeProperties.getText());
        wfTask.setDataId(dataId);
        wfTask.setUserId(String.valueOf(loginUser.getUserId()));
        wfTask.setUserName(loginUser.getUsername());
        wfTask.setTaskDone("0");
        wfTask.setProcessStarter(wfInstance.getProcessStarter());
        wfTask.setProcessStarterId(wfInstance.getProcessStarterId());
        wfTask.setNextEdgeId(wfInstanceStartDto.getNextEdgeId());
        wfTask.setNextEdgeName(FlowChartUtils.getEdgeNameById(flowChart, wfInstanceStartDto.getNextEdgeId()));
        wfTask.setNextNodeId(wfInstance.getNodeId());
        wfTask.setNextNodeName(wfInstance.getNodeName());
        wfTaskService.createTask(wfTask);
        // 完成
        wfTaskService.handle(wfTask.getId(),
                wfInstanceStartDto.getCommentContent(),
                wfTask.getNextEdgeId(),
                wfTask.getNextEdgeName(),
                wfTask.getNextNodeName(),
                wfTask.getNextNodeName(),
                true);
        // 执行计算规则
        wfTaskService.executeWFComputeRule(wfInstance.getTableId(), dataId, currentEdge);
        // 进入下一环节
        wfTaskService.enterNextNode(wfInstance, nextNode, table.getMemTableName(), table.getChildFlag(), table.getTableLevel(), memTableDataVo.getRows(), unitColumnVal, wfStateColumn);
        return wfInstance;
    }

    private WfInstance startCubeProcess(WfInstanceStartDTO wfInstanceStartDto) {
        // 校验是否已经有发起的运行中
        WfInstance existWfInstance = this.getOne(Wrappers.<WfInstance>lambdaQuery()
                .eq(WfInstance::getDefineId, wfInstanceStartDto.getWfDimDefineId())
                .eq(WfInstance::getFlowType, wfInstanceStartDto.getFlowType())
                .orderByDesc(WfInstance::getCreateTime)
                .last("limit 1")
        );
        if (existWfInstance != null && !(existWfInstance.getInstanceState() == WfInstanceStateEnums.COMPLETE || existWfInstance.getInstanceState() == WfInstanceStateEnums.STOP)) {
            throw new WfRuntimeException("该流程正在运行中，请勿重复发起");
        }

        WfDefine wfDefine = wfDefineService.getById(wfInstanceStartDto.getWfDimDefineId());
        if (wfDefine == null) {
            throw new WfRuntimeException("未查询到流程定义");
        }
        List<WfDimDefineDetailVO> list = wfDefineService.getByDefineId(wfInstanceStartDto.getWfDimDefineId(), null, false, null);
        if (CollectionUtils.isEmpty(list)) {
            throw new WfRuntimeException("流程配置的审批维度未查询到数据");
        }
        // 校验是否都有审批人
        Map<Long, WfDimDefineDetailVO> wfDimDefineDetailMap = StreamUtils.toMap(list, WfDimDefineDetailVO::getId, Function.identity());
        List<Long> userEmptyDimIds = list.stream()
                .filter(v -> StringUtils.isEmpty(v.getUserId()))
                .map(WfDimDefineDetailVO::getDimId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userEmptyDimIds)) {
            List<DimInstance> dimInstanceList = dimInstanceService.listByIds(userEmptyDimIds);
            throw new WfRuntimeException("维度：【" + StreamUtils.join(dimInstanceList, DimInstance::getDimName) + "】的审批人不能为空");
        }
        // 拿到所有的归口维度
        Set<Long> wfDimDefineDetailIds = list.stream()
                .filter(v -> StringUtils.isNotEmpty(v.getCentralizedDimId()))
                .map(WfDimDefineDetailVO::getId)
                .collect(Collectors.toSet());
        List<WfDimDefineCentralize> centralizes;
        if (CollectionUtils.isNotEmpty(wfDimDefineDetailIds)) {
            centralizes = wfDimDefineDetailService.getCentralizes(wfDimDefineDetailIds);
            Map<Long, List<WfDimDefineCentralize>> map = centralizes.stream()
                    .filter(v -> StringUtils.isEmpty(v.getUserId()))
                    .collect(Collectors.groupingBy(WfDimDefineCentralize::getDimDefineDetailId));
            if (MapUtils.isNotEmpty(map)) {
                Set<Long> collect = map.keySet().stream()
                        .map(v -> MapUtils.getObject(wfDimDefineDetailMap, v).getDimId()).collect(Collectors.toSet());
                List<DimInstance> dimInstanceList = dimInstanceService.listByIds(collect);
                Map<Long, DimInstance> dimInstanceMap = StreamUtils.toMap(dimInstanceList, v -> Long.valueOf(v.getId()), Function.identity());
                StringBuilder stringBuilder = new StringBuilder();
                for (Map.Entry<Long, List<WfDimDefineCentralize>> entry : map.entrySet()) {
                    DimInstance dimInstance = MapUtils.getObject(dimInstanceMap, MapUtils.getObject(wfDimDefineDetailMap, entry.getKey()).getDimId());
                    String centralizeNames = StreamUtils.join(entry.getValue(), WfDimDefineCentralize::getCentralizeName);
                    stringBuilder.append("维度：【").append(dimInstance.getDimName()).append("】的归口维度：【").append(centralizeNames).append("】审批人不能为空\n");
                }
                throw new WfRuntimeException(stringBuilder.toString());
            }
        } else {
            centralizes = Collections.emptyList();
        }
        //设置归口维度属性
        if (CollectionUtils.isNotEmpty(centralizes)) {
            Map<Long, List<WfDimDefineCentralize>> longListMap = StreamUtils.groupByKey(centralizes, WfDimDefineCentralize::getDimDefineDetailId);
            list.forEach(wfDimDefineDetail -> wfDimDefineDetail.setCentralizeList(MapUtils.getObject(longListMap, wfDimDefineDetail.getId())));
        }
        DimTable table = dimTableService.getById(wfDefine.getTableId());
        if (table == null) {
            throw new WfRuntimeException("未查询到多维表");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        WfInstance wfInstance = new WfInstance();
        wfInstance.setInstanceState(WfInstanceStateEnums.RUNNING);
        wfInstance.setDefineId(wfDefine.getId());
        wfInstance.setDefineName(wfDefine.getFlowName());
        wfInstance.setProcessStarter(loginUser.getUsername());
        wfInstance.setProcessStarterId(String.valueOf(loginUser.getUserId()));
        wfInstance.setTableId(wfDefine.getTableId());
        wfInstance.setTableName(table.getTableName());
        wfInstance.setFlowType(WfInstanceTypeEnums.CUBE);
        wfInstance.setStartTime(new Date());
        this.save(wfInstance);
        // 发布流程发起事件
        WfEventSource wfEventSource = new WfEventSource();
        BeanUtils.copyProperties(wfInstance, wfEventSource);
        wfEventSource.setEventType(WfEventTypeEnums.START_PROCESS);
        wfEventSource.setMemTableName(table.getMemTableName());
        wfEventSource.setFlowType(wfInstanceStartDto.getFlowType());
        wfEventSource.setUserId(String.valueOf(SecurityUtils.getUserId()));
        wfEventSource.setUserName(SecurityUtils.getUsername());
        SpringUtils.publishEvent(new WfEvent(wfEventSource));

        // 获取维度父子关系
        Set<Long> dimIdSet = StreamUtils.toSet(list, WfDimDefineDetailVO::getDimId);
        List<DimInstance> dimInstanceList = dimInstanceService.listByIds(dimIdSet);
        int dimInstanceListSize = dimInstanceList.size();
        Map<String, Long> dimInstanceParentIdMap = Maps.newHashMapWithExpectedSize(dimInstanceListSize);
        Map<Long, List<String>> dimInstanceChildIdMap = Maps.newHashMapWithExpectedSize(dimInstanceListSize);
        for (DimInstance dimInstance : dimInstanceList) {
            String id = String.valueOf(dimInstance.getId());
            if (dimInstance.getParentId() != null) {
                Long parentId = Long.valueOf(dimInstance.getParentId());
                dimInstanceParentIdMap.put(id, parentId);
                List<String> childList = dimInstanceChildIdMap.computeIfAbsent(parentId, k -> new ArrayList<>());
                childList.add(id);
            }
        }

        // 总行审批人可以看分行全部，总行的归口审批人只看分行归口的数据
        // 找到所有审批人
        Set<String> userIdSet = new HashSet<>();
        for (WfDimDefineDetailVO wfDimDefineDetail : list) {
            userIdSet.addAll(Arrays.asList(wfDimDefineDetail.getUserId().split(",")));
            if (CollectionUtils.isNotEmpty(wfDimDefineDetail.getCentralizeList())) {
                for (WfDimDefineCentralize centralize : wfDimDefineDetail.getCentralizeList()) {
                    userIdSet.addAll(Arrays.asList(centralize.getUserId().split(",")));
                }
            }
        }
        List<SysUser> sysUsers = userService.listByIds(userIdSet);
        Map<String, String> userIdNameMap = StreamUtils.toMap(sysUsers, v -> String.valueOf(v.getUserId()), SysUser::getNickName);
//        // 构造父任务id
//        Map<Long, WfTask> dimIdTaskMap = new HashMap<>();
        List<WfTask> wfTaskList = new ArrayList<>();
        List<WfVariable> wfVariableList = new ArrayList<>();
        for (WfDimDefineDetailVO wfDimDefineDetail : list) {
            String[] userIds = wfDimDefineDetail.getUserId().split(",");
            String nodeId = String.valueOf(wfDimDefineDetail.getDimId());
            String parentNodeId = MapUtils.getString(dimInstanceParentIdMap, nodeId);
            for (String userIdStr : userIds) {
                // 创建所有审批人的任务
                WfTask wfTask = new WfTask();
                wfTask.setDefineId(wfInstance.getDefineId());
                wfTask.setDefineName(wfInstance.getDefineName());
                wfTask.setInstanceId(wfInstance.getId());
                wfTask.setTableId(wfInstance.getTableId());
                wfTask.setTableName(wfInstance.getTableName());
                wfTask.setNodeId(nodeId);
                wfTask.setUserId(userIdStr);
                wfTask.setUserName(MapUtils.getString(userIdNameMap, userIdStr));
                wfTask.setTaskDone("0");
                wfTask.setProcessStarter(wfInstance.getProcessStarter());
                wfTask.setProcessStarterId(wfInstance.getProcessStarterId());
                wfTask.setParentNodeId(parentNodeId);
                wfTask.setFlowType(WfInstanceTypeEnums.CUBE);
                wfTask.setTaskState(WfTaskStateEnums.PRE_SUBMIT);
                wfTaskList.add(wfTask);
                // 流程变量
                WfVariable taskStateVariable = new WfVariable();
                taskStateVariable.setInstanceId(wfInstance.getId());
                taskStateVariable.setNodeId(wfTask.getNodeId());
                taskStateVariable.setVariableKey(WfRtConstants.WfVariableKey.TASK_STATE);
                taskStateVariable.setVariableVal(wfTask.getTaskState().name());
                wfVariableList.add(taskStateVariable);
                WfVariable parentNodeIdVariable = new WfVariable();
                parentNodeIdVariable.setInstanceId(wfInstance.getId());
                parentNodeIdVariable.setNodeId(wfTask.getNodeId());
                parentNodeIdVariable.setVariableKey(WfRtConstants.WfVariableKey.PARENT_NODE_ID);
                parentNodeIdVariable.setVariableVal(wfTask.getParentNodeId());
                wfVariableList.add(parentNodeIdVariable);
            }
            if (CollectionUtils.isNotEmpty(wfDimDefineDetail.getCentralizeList())) {
                for (WfDimDefineCentralize centralize : wfDimDefineDetail.getCentralizeList()) {
                    String[] centralizeUserIds = centralize.getUserId().split(",");
                    for (String userIdStr : centralizeUserIds) {
                        // 创建归口维度所有审批人的任务
                        WfTask wfTask = new WfTask();
                        wfTask.setDefineId(wfInstance.getDefineId());
                        wfTask.setDefineName(wfInstance.getDefineName());
                        wfTask.setInstanceId(wfInstance.getId());
                        wfTask.setTableId(wfInstance.getTableId());
                        wfTask.setTableName(wfInstance.getTableName());
                        wfTask.setNodeId(nodeId);
                        wfTask.setUserId(userIdStr);
                        wfTask.setUserName(MapUtils.getString(userIdNameMap, userIdStr));
                        wfTask.setTaskDone("0");
                        wfTask.setProcessStarter(wfInstance.getProcessStarter());
                        wfTask.setProcessStarterId(wfInstance.getProcessStarterId());
                        wfTask.setParentNodeId(parentNodeId);
                        wfTask.setFlowType(WfInstanceTypeEnums.CUBE);
                        // 归口审批状态为未处理
                        wfTask.setTaskState(WfTaskStateEnums.PRE_HANDLE);
                        wfTask.setCentralizeDimDefineId(centralize.getId());
                        wfTaskList.add(wfTask);

                        // 子节点任务流程变量
                        List<String> childList = dimInstanceChildIdMap.get(wfDimDefineDetail.getDimId());
                        if (CollectionUtils.isNotEmpty(childList)) {
                            for (String childId : childList) {
                                // 流程变量
                                WfVariable taskStateVariable = new WfVariable();
                                taskStateVariable.setInstanceId(wfInstance.getId());
                                taskStateVariable.setNodeId(childId);
                                taskStateVariable.setCentralizeId(String.valueOf(centralize.getId()));
                                taskStateVariable.setVariableKey(WfRtConstants.WfVariableKey.TASK_STATE);
                                taskStateVariable.setVariableVal(wfTask.getTaskState().name());
                                wfVariableList.add(taskStateVariable);
                            }
                        }
                    }
                }
            }
        }
        wfTaskService.saveBatch(wfTaskList);
        // 保存流程变量
        wfVariableService.saveBatch(wfVariableList);

        wfTaskList.forEach(wfTask -> {
            // 发布创建任务事件
            WfEventSource wfTaskCreateEventSource = new WfEventSource();
            BeanUtils.copyProperties(wfTask, wfTaskCreateEventSource);
            wfTaskCreateEventSource.setEventType(WfEventTypeEnums.TASK_CREATED);
            wfTaskCreateEventSource.setUserId(String.valueOf(SecurityUtils.getUserId()));
            wfTaskCreateEventSource.setUserName(SecurityUtils.getUsername());
            SpringUtils.publishEvent(new WfEvent(wfTaskCreateEventSource));
        });
        return wfInstance;
    }

    @Override
    public List<FlowChart.EdgeDTO> getNextEdges(Integer tableId, Long taskId, String nodeId, String dataId) {
        // 说明是发起审批
        WfDefine wfDefine = wfDefineService.getByTableId(tableId, true);
        if (wfDefine == null) {
            throw new WfRuntimeException("未查询到流程定义");
        }
        if (StringUtils.isEmpty(wfDefine.getFlowchartJson())) {
            throw new WfRuntimeException("请先设计流程图");
        }
        FlowChart flowChart = JSON.parseObject(wfDefine.getFlowchartJson(), FlowChart.class);
        if (taskId == null) {
            if (StringUtils.isEmpty(nodeId)) {
                nodeId = FlowChartUtils.getStartNode(flowChart).getId();
            }
        }
        List<FlowChart.EdgeDTO> edges = FlowChartUtils.getEdgesBySourceId(flowChart, nodeId);
        Iterator<FlowChart.EdgeDTO> iterator = edges.iterator();
        while (iterator.hasNext()) {
            FlowChart.EdgeDTO edge = iterator.next();
            if (edge.getProperties() != null && StringUtils.isNotEmpty(edge.getProperties().getVisibleCondition())) {
                String visibleCondition = edge.getProperties().getVisibleCondition();
                if (!ruleService.validateVisibleCondition(tableId, dataId, ruleService.handleRuleExpress(visibleCondition))) {
                    iterator.remove();
                }
            }
        }
        return edges;
    }

    @Override
    public FlowChart.NodeDTO getCurrentNode(Integer tableId, Long instanceId) {
        if (instanceId == null) {
            WfDefine wfDefine = wfDefineService.getByTableId(tableId, true);
            if (wfDefine == null) {
                throw new WfRuntimeException("未查询到流程定义");
            }
            if (StringUtils.isEmpty(wfDefine.getFlowchartJson())) {
                throw new WfRuntimeException("请先设计流程图");
            }
            FlowChart flowChart = JSON.parseObject(wfDefine.getFlowchartJson(), FlowChart.class);
            return FlowChartUtils.getStartNode(flowChart);
        } else {
            WfInstance wfInstance = this.getById(instanceId);
            if (wfInstance == null) {
                throw new WfRuntimeException("未查询到流程实例");
            }
            WfDefine wfDefine = wfDefineService.getById(wfInstance.getDefineId());
            if (wfDefine == null) {
                throw new WfRuntimeException("未查询到流程定义");
            }
            if (StringUtils.isEmpty(wfDefine.getFlowchartJson())) {
                throw new WfRuntimeException("请先设计流程图");
            }
            FlowChart flowChart = JSON.parseObject(wfDefine.getFlowchartJson(), FlowChart.class);
            return FlowChartUtils.getNodeById(flowChart, wfInstance.getNodeId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WfInstance stopProcess(WfInstanceStartDTO wfInstanceStartDto) {
        // 校验是否已经有发起的运行中
        WfInstance existWfInstance = this.getOne(Wrappers.<WfInstance>lambdaQuery()
                .eq(WfInstance::getDefineId, wfInstanceStartDto.getWfDimDefineId())
                .eq(WfInstance::getFlowType, wfInstanceStartDto.getFlowType())
                .orderByDesc(WfInstance::getCreateTime)
                .last("limit 1")
        );
        if (existWfInstance == null) {
            throw new WfRuntimeException("该流程未发起，无法终止");
        }
        if (existWfInstance.getInstanceState() == WfInstanceStateEnums.COMPLETE || existWfInstance.getInstanceState() == WfInstanceStateEnums.STOP) {
            throw new WfRuntimeException("该流程状态已结束，无法终止");
        }
        existWfInstance.setInstanceState(WfInstanceStateEnums.STOP);
        this.updateById(existWfInstance);
        // 删除任务
        wfTaskService.remove(Wrappers.<WfTask>lambdaQuery().eq(WfTask::getInstanceId, existWfInstance.getId()));
        // 添加流程记录
        WfHistory wfHistory = new WfHistory();
        wfHistory.setDefineId(existWfInstance.getDefineId());
        wfHistory.setTableId(existWfInstance.getTableId());
        wfHistory.setInstanceId(existWfInstance.getId());
        wfHistory.setCommentContent(SecurityUtils.getLoginUser().getUser().getNickName() + "终止流程");
        wfHistoryService.insertCubeWfHistory(wfHistory);
        return existWfInstance;
    }

}
