package com.dcube.workflow.runtime.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.constant.BizConstants;
import com.dcube.biz.domain.DimInstance;
import com.dcube.biz.domain.Table;
import com.dcube.biz.dto.CellDto;
import com.dcube.biz.dto.RowDto;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.query.TableListQuery;
import com.dcube.biz.service.IDimInstanceService;
import com.dcube.biz.service.ITableService;
import com.dcube.biz.vo.MemTableDataVo;
import com.dcube.common.core.domain.entity.SysRole;
import com.dcube.common.core.domain.entity.SysUser;
import com.dcube.common.core.domain.model.LoginUser;
import com.dcube.common.utils.SecurityUtils;
import com.dcube.common.utils.StreamUtils;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.spring.SpringUtils;
import com.dcube.rule.grid.service.IRuleService;
import com.dcube.system.service.ISysRoleService;
import com.dcube.system.service.ISysUserService;
import com.dcube.workflow.define.constants.enums.FlowNodeTypeEnums;
import com.dcube.workflow.define.constants.enums.WfDefineFlowTypeEnums;
import com.dcube.workflow.define.domain.WfDefine;
import com.dcube.workflow.define.domain.WfDimDefineCentralize;
import com.dcube.workflow.define.flowchart.FlowChart;
import com.dcube.workflow.define.service.IWfDefineService;
import com.dcube.workflow.define.service.IWfDimDefineCentralizeService;
import com.dcube.workflow.define.service.IWfDimDefineDetailService;
import com.dcube.workflow.define.utils.FlowChartUtils;
import com.dcube.workflow.define.vo.WfDimDefineDetailVO;
import com.dcube.workflow.runtime.constants.WfRtConstants;
import com.dcube.workflow.runtime.constants.enums.WfEventTypeEnums;
import com.dcube.workflow.runtime.constants.enums.WfInstanceStateEnums;
import com.dcube.workflow.runtime.constants.enums.WfInstanceTypeEnums;
import com.dcube.workflow.runtime.constants.enums.WfTaskStateEnums;
import com.dcube.workflow.runtime.domain.WfInstance;
import com.dcube.workflow.runtime.domain.WfTask;
import com.dcube.workflow.runtime.domain.WfVariable;
import com.dcube.workflow.runtime.dto.WfTaskDTO;
import com.dcube.workflow.runtime.exception.WfRuntimeException;
import com.dcube.workflow.runtime.listener.event.WfEvent;
import com.dcube.workflow.runtime.listener.event.WfEventSource;
import com.dcube.workflow.runtime.mapper.WfTaskMapper;
import com.dcube.workflow.runtime.service.IWfTaskService;
import com.dcube.workflow.runtime.service.IWfVariableService;
import com.dcube.workflow.runtime.vo.NextEdgeInfoVO;
import com.dcube.workflow.runtime.vo.SubmitInfoEdgeVO;
import com.dcube.workflow.runtime.vo.ViewNodeStateVO;
import com.dcube.workflow.runtime.vo.WfTaskVO;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 流程任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Service
@Slf4j
public class WfTaskServiceImpl extends ServiceImpl<WfTaskMapper, WfTask> implements IWfTaskService {
    @Autowired
    private WfTaskMapper wfTaskMapper;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysRoleService sysRoleService;
    @Autowired
    private IWfDefineService wfDefineService;
    @Autowired
    private ITableService tableService;
    @Autowired
    private IRuleService ruleService;
    @Autowired
    private IDimInstanceService dimInstanceService;
    @Autowired
    private IWfVariableService wfVariableService;
    @Autowired
    private IWfDimDefineDetailService wfDimDefineDetailService;
    @Autowired
    private IWfDimDefineCentralizeService wfDimDefineCentralizeService;

    /**
     * 查询流程任务
     *
     * @param id 流程任务主键
     * @return 流程任务
     */
    @Override
    public WfTask selectCubeWfTaskById(Long id) {
        return wfTaskMapper.selectCubeWfTaskById(id);
    }


    /**
     * 查询用户流程任务数量
     *
     * @param userId 流程任务
     * @return 流程任务数量
     */
    @Override
    public Map<String, Object> selectWfTaskCountByUserId(String userId) {
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(2);
        map.put("todo", 0);
        map.put("done", 0);
        List<Map<String, Object>> list = wfTaskMapper.selectWfTaskCountByUserId(userId);
        if (CollectionUtils.isEmpty(list)) {
            return map;
        }
        for (Map<String, Object> map1 : list) {
            String taskDone = MapUtils.getString(map1, "task_done");
            if (StringUtils.equals(taskDone, "0")) {
                map.put("todo", MapUtils.getObject(map1, "val"));
            } else {
                map.put("done", MapUtils.getObject(map1, "val"));
            }
        }
        return map;
    }

    /**
     * 查询流程任务列表
     *
     * @param wfTask 流程任务
     * @return 流程任务
     */
    @Override
    public List<WfTaskVO> selectCubeWfTaskList(WfTaskDTO wfTask) {
        List<WfTaskVO> wfTaskVOS = wfTaskMapper.selectCubeWfTaskList(wfTask);
        if (CollectionUtils.isEmpty(wfTaskVOS)) {
            return Collections.emptyList();
        }
        Set<String> nodeIds = wfTaskVOS.stream()
                .filter(v -> v.getFlowType() == WfDefineFlowTypeEnums.CUBE)
                .map(WfTaskVO::getNodeId)
                .collect(Collectors.toSet());
        Map<String, String> dimIdNameMap;
        if (CollectionUtils.isNotEmpty(nodeIds)) {
            List<DimInstance> dimInstanceList = dimInstanceService.listByIds(nodeIds);
            dimIdNameMap = StreamUtils.toMap(dimInstanceList, v -> String.valueOf(v.getId()), DimInstance::getDimName);
        } else {
            dimIdNameMap = Collections.emptyMap();
        }
        Set<Long> centralizeIds = wfTaskVOS.stream()
                .filter(v -> v.getCentralizeDimDefineId() != null)
                .map(WfTaskVO::getCentralizeDimDefineId)
                .collect(Collectors.toSet());
        Map<Long, String> centralizeIdNameMap;
        if (CollectionUtils.isNotEmpty(centralizeIds)) {
            List<WfDimDefineCentralize> wfDimDefineCentralizes = wfDimDefineCentralizeService.listByIds(centralizeIds);
            centralizeIdNameMap = StreamUtils.toMap(wfDimDefineCentralizes, WfDimDefineCentralize::getId, WfDimDefineCentralize::getCentralizeName);
        } else {
            centralizeIdNameMap = Collections.emptyMap();
        }
        for (WfTaskVO wfTaskVO : wfTaskVOS) {
            boolean complete = wfTaskVO.getInstanceState() == WfInstanceStateEnums.COMPLETE;
            wfTaskVO.setCompleted(complete);
            wfTaskVO.setProcessDone(complete);
            if (StringUtils.isNotEmpty(wfTaskVO.getDataId())) {
                wfTaskVO.setDataSize(String.valueOf(wfTaskVO.getDataId().split(",").length));
            } else {
                wfTaskVO.setDataSize("0");
            }
            if (StringUtils.equals(wfTaskVO.getTaskDone(), "1")) {
                wfTaskVO.setStayTime(TimeUnit.MILLISECONDS.toHours(wfTaskVO.getCost()) + "时");
            } else {
                wfTaskVO.setStayTime(DateUtil.between(wfTaskVO.getCreateTime(), new Date(), DateUnit.HOUR) + "时");
            }
            if (wfTaskVO.getFlowType() != null) {
                wfTaskVO.setFlowTypeName(wfTaskVO.getFlowType().getDesc());
            }
            if (wfTaskVO.getFlowType() == WfDefineFlowTypeEnums.CUBE) {
                if (wfTaskVO.getCentralizeDimDefineId() == null) {
                    wfTaskVO.setNodeName(MapUtils.getString(dimIdNameMap, wfTaskVO.getNodeId()));
                } else {
                    wfTaskVO.setNodeName(MapUtils.getString(centralizeIdNameMap, wfTaskVO.getCentralizeDimDefineId()));
                }
            }
        }
        return wfTaskVOS;
    }

    /**
     * 新增流程任务
     *
     * @param wfTask 流程任务
     * @return 结果
     */
    @Override
    public int insertCubeWfTask(WfTask wfTask) {
        return wfTaskMapper.insertCubeWfTask(wfTask);
    }

    /**
     * 修改流程任务
     *
     * @param wfTask 流程任务
     * @return 结果
     */
    @Override
    public int updateCubeWfTask(WfTask wfTask) {
        return wfTaskMapper.updateCubeWfTask(wfTask);
    }

    /**
     * 批量删除流程任务
     *
     * @param ids 需要删除的流程任务主键
     * @return 结果
     */
    @Override
    public int deleteCubeWfTaskByIds(Long[] ids) {
        return wfTaskMapper.deleteCubeWfTaskByIds(ids);
    }

    /**
     * 删除流程任务信息
     *
     * @param id 流程任务主键
     * @return 结果
     */
    @Override
    public int deleteCubeWfTaskById(Long id) {
        return wfTaskMapper.deleteCubeWfTaskById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WfTask createTask(WfTask wfTask) {
        this.save(wfTask);
        // 发布创建任务事件
        WfEventSource wfEventSource = new WfEventSource();
        BeanUtils.copyProperties(wfTask, wfEventSource);
        wfEventSource.setEventType(WfEventTypeEnums.TASK_CREATED);
        wfEventSource.setUserId(String.valueOf(SecurityUtils.getUserId()));
        wfEventSource.setUserName(SecurityUtils.getUsername());
        SpringUtils.publishEvent(new WfEvent(wfEventSource));
        return wfTask;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WfTask handle(WfTaskDTO wfTaskDto) {
        return handle(wfTaskDto.getId(),
                wfTaskDto.getCommentContent(),
                wfTaskDto.getNextEdgeId(),
                wfTaskDto.getNextEdgeName(),
                wfTaskDto.getNextNodeId(),
                wfTaskDto.getNextNodeName(),
                false);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public WfTask handle(Long id,
                         String commentContent,
                         String nextEdgeId,
                         String nextEdgeName,
                         String nextNodeId,
                         String nextNodeName,
                         boolean startProcess) {
        WfTask wfTask = this.getById(id);
        if (wfTask == null) {
            throw new WfRuntimeException("未查询到任务");
        }
        Date approveTime = new Date();
        long cost = startProcess ? 0 : DateUtil.betweenMs(approveTime, wfTask.getCreateTime());
        wfTask.setApproveTime(approveTime);
        wfTask.setCommentContent(commentContent);
        wfTask.setNextEdgeId(nextEdgeId);
        wfTask.setNextEdgeName(nextEdgeName);
        wfTask.setNextNodeId(nextNodeId);
        wfTask.setNextNodeName(nextNodeName);
        wfTask.setCost(cost);
        return handle(wfTask);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WfTask handle(WfTask wfTask) {
        LambdaUpdateWrapper<WfTask> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(WfTask::getId, wfTask.getId());
        updateWrapper.set(WfTask::getTaskDone, "1");
        updateWrapper.set(WfTask::getCommentContent, wfTask.getCommentContent());
        updateWrapper.set(WfTask::getApproveTime, wfTask.getApproveTime());
        updateWrapper.set(WfTask::getCost, wfTask.getCost());
        if (wfTask.getCentralizeDimDefineId() == null) {
            updateWrapper.set(WfTask::getNextEdgeId, wfTask.getNextEdgeId());
            updateWrapper.set(WfTask::getNextEdgeName, wfTask.getNextEdgeName());
            updateWrapper.set(WfTask::getNextNodeId, wfTask.getNextNodeId());
            updateWrapper.set(WfTask::getNextNodeName, wfTask.getNextNodeName());
            updateWrapper.set(WfTask::getTaskState, wfTask.getTaskState());
        }
        updateWrapper.set(WfTask::getOperateUserId, wfTask.getOperateUserId());
        boolean update = this.update(updateWrapper);
        if (!update) {
            throw new WfRuntimeException("任务已被处理");
        }

        // 删除当前节点的其他未完成任务
        LambdaUpdateWrapper<WfTask> deleteOtherTaskWrapper = new LambdaUpdateWrapper<>();
        deleteOtherTaskWrapper.eq(WfTask::getInstanceId, wfTask.getInstanceId());
        deleteOtherTaskWrapper.eq(WfTask::getNodeId, wfTask.getNodeId());
        deleteOtherTaskWrapper.eq(WfTask::getTaskDone, "0");
        deleteOtherTaskWrapper.eq(wfTask.getCentralizeDimDefineId() != null, WfTask::getCentralizeDimDefineId, wfTask.getCentralizeDimDefineId());
        this.remove(deleteOtherTaskWrapper);

        // 发布审批完成事件
        WfEventSource wfEventSource = new WfEventSource();
        BeanUtils.copyProperties(wfTask, wfEventSource);
        wfEventSource.setCommentContent(wfTask.getCommentContent());
        wfEventSource.setEventType(WfEventTypeEnums.TASK_HANDLE);
        wfEventSource.setCost(wfTask.getCost());
        wfEventSource.setApproveTime(wfTask.getApproveTime());
        wfEventSource.setOperateType(wfTask.getOperateType());
        wfEventSource.setUserId(String.valueOf(SecurityUtils.getUserId()));
        wfEventSource.setUserName(SecurityUtils.getUsername());
        SpringUtils.publishEvent(new WfEvent(wfEventSource));
        return wfTask;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enterNextNode(WfInstance wfInstance,
                              FlowChart.NodeDTO nextNode,
                              String memTableName,
                              String childFlag,
                              Integer tableLevel,
                              List<RowDto> rows,
                              String deptName,
                              int wfStateColumnCodeIndex) {
        // 发布进入环节事件
        WfEventSource wfEventSource = new WfEventSource();
        BeanUtils.copyProperties(wfInstance, wfEventSource);
        wfEventSource.setEventType(WfEventTypeEnums.ENTER_NODE);
        wfEventSource.setMemTableName(memTableName);
        wfEventSource.setTableChildFlag(childFlag);
        wfEventSource.setTableLevel(tableLevel);
        wfEventSource.setRows(rows);
        wfEventSource.setUserId(String.valueOf(SecurityUtils.getUserId()));
        wfEventSource.setUserName(SecurityUtils.getUsername());
        SpringUtils.publishEvent(new WfEvent(wfEventSource));

        if (nextNode.getType() == FlowNodeTypeEnums.END) {
            // 发布流程结束事件
            WfEventSource processEndEvent = new WfEventSource();
            BeanUtils.copyProperties(wfInstance, processEndEvent);
            processEndEvent.setEventType(WfEventTypeEnums.PROCESS_END);
            processEndEvent.setInstanceId(wfInstance.getId());
            processEndEvent.setMemTableName(memTableName);
            processEndEvent.setTableChildFlag(childFlag);
            processEndEvent.setTableLevel(tableLevel);
            updateWfStateColumnData(rows, wfStateColumnCodeIndex, BizConstants.WfState.APPROVED);
            processEndEvent.setRows(rows);
            processEndEvent.setUserId(String.valueOf(SecurityUtils.getUserId()));
            processEndEvent.setUserName(SecurityUtils.getUsername());
            SpringUtils.publishEvent(new WfEvent(processEndEvent));
        } else if (nextNode.getType() == FlowNodeTypeEnums.TASK) {
            if (nextNode.getProperties() == null || StringUtils.isEmpty(nextNode.getProperties().getRoleId())) {
                log.error("流程下一环节审批人角色配置为空");
                return;
            }
            String roleId = nextNode.getProperties().getRoleId();
            List<SysRole> sysRoles = sysRoleService.selectRoleById(roleId);
            if (CollectionUtils.isEmpty(sysRoles)) {
                log.error("未查询到流程下一环节审批人角色");
                return;
            }
//            Map<Long, String> sysRoleMap = sysRoles.stream().collect(Collectors.toMap(SysRole::getRoleId, SysRole::getRoleName, (v1, v2) -> v2));
            String roleName = sysRoles.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
            List<SysUser> sysUsers = sysUserService.selectUserByRoleId(roleId, deptName);
            if (CollectionUtils.isEmpty(sysUsers)) {
//                throw new WfRuntimeException("流程下一环节审批人不能为空");
                log.error("流程下一环节审批人为空");
                return;
            }
            for (SysUser sysUser : sysUsers) {
                WfTask wfTask = new WfTask();
                wfTask.setDefineId(wfInstance.getDefineId());
                wfTask.setDefineName(wfInstance.getDefineName());
                wfTask.setInstanceId(wfInstance.getId());
                wfTask.setTableId(wfInstance.getTableId());
                wfTask.setTableName(wfInstance.getTableName());
                wfTask.setNodeId(nextNode.getId());
                wfTask.setNodeName(nextNode.getProperties().getText());
                wfTask.setDataId(wfInstance.getDataId());
                wfTask.setUserId(String.valueOf(sysUser.getUserId()));
                wfTask.setUserName(sysUser.getUserName());
                wfTask.setTaskDone("0");
                wfTask.setProcessStarter(wfInstance.getProcessStarter());
                wfTask.setProcessStarterId(wfInstance.getProcessStarterId());
                wfTask.setApproveRoleId(roleId);
                wfTask.setApproveRoleName(roleName);
                SpringUtils.getAopProxy(this).createTask(wfTask);
            }
        } else if (nextNode.getType() == FlowNodeTypeEnums.START) {
            // 流程退回给发起人，状态列修改为未提交，结束流程

            // 发布流程结束事件
            WfEventSource processEndEvent = new WfEventSource();
            BeanUtils.copyProperties(wfInstance, processEndEvent);
            processEndEvent.setEventType(WfEventTypeEnums.PROCESS_END);
            processEndEvent.setInstanceId(wfInstance.getId());
            processEndEvent.setMemTableName(memTableName);
            processEndEvent.setTableChildFlag(childFlag);
            processEndEvent.setTableLevel(tableLevel);
            updateWfStateColumnData(rows, wfStateColumnCodeIndex, BizConstants.WfState.PRE_SUBMIT);
            processEndEvent.setRows(rows);
            processEndEvent.setReturn2Starter(true);
            processEndEvent.setUserId(String.valueOf(SecurityUtils.getUserId()));
            processEndEvent.setUserName(SecurityUtils.getUsername());
            SpringUtils.publishEvent(new WfEvent(processEndEvent));
        }
    }

    private void updateWfStateColumnData(List<RowDto> rows, int wfStateColumnCodeIndex, String wfState) {
        if (CollectionUtils.isEmpty(rows)) {
            return;
        }
        for (RowDto row : rows) {
            for (CellDto cell : row.getCells()) {
                if (cell.getOriginalValue() != null) {
                    cell.setCellValue(String.valueOf(cell.getOriginalValue()));
                }
            }
            row.getCells().get(wfStateColumnCodeIndex).setCellValue(wfState);
        }
    }

    @Override
    public NextEdgeInfoVO nextEdgeInfo(Integer tableId, Long taskId, String dataId, WfInstanceTypeEnums flowType) {
        NextEdgeInfoVO nextEdgeInfoVO = new NextEdgeInfoVO();
        if (flowType == WfInstanceTypeEnums.TABLE) {
            FlowChart flowChart;
            FlowChart.NodeDTO currentNode;
            if (taskId == null) {
                WfDefine wfDefine = wfDefineService.getByTableId(tableId, true);
                if (wfDefine == null) {
                    throw new WfRuntimeException("未查询到流程定义");
                }
                if (StringUtils.isEmpty(wfDefine.getFlowchartJson())) {
                    throw new WfRuntimeException("请先设计流程图");
                }
                flowChart = JSON.parseObject(wfDefine.getFlowchartJson(), FlowChart.class);
                currentNode = FlowChartUtils.getStartNode(flowChart);
            } else {
                WfTask wfTask = this.getById(taskId);
                if (wfTask == null) {
                    throw new WfRuntimeException("未查询到任务");
                }
                WfDefine wfDefine = wfDefineService.getById(wfTask.getDefineId());
                if (wfDefine == null) {
                    throw new WfRuntimeException("未查询到流程定义");
                }
                if (StringUtils.isEmpty(wfDefine.getFlowchartJson())) {
                    throw new WfRuntimeException("请先设计流程图");
                }
                flowChart = JSON.parseObject(wfDefine.getFlowchartJson(), FlowChart.class);
                currentNode = FlowChartUtils.getNodeById(flowChart, wfTask.getNodeId());
            }
            Table table = tableService.getById(tableId);
            if (table == null) {
                throw new WfRuntimeException("未查询到二维表");
            }

            TableListQuery tableListQuery = new TableListQuery();
            tableListQuery.setTableId(tableId);
            tableListQuery.setFilterDataId(dataId);
            tableListQuery.setCurrentPage(1);
            tableListQuery.setPageSize(dataId.split(",").length);
            LoginUser loginUser = SecurityUtils.getLoginUser();
            PageInfo<MemTableDataVo> pageInfo = tableService.queryTableData(tableListQuery, loginUser);
            if (pageInfo == null
                    || CollectionUtils.isEmpty(pageInfo.getList())
                    || pageInfo.getList().get(0) == null
                    || CollectionUtils.isEmpty(pageInfo.getList().get(0).getRows())) {
                throw new WfRuntimeException("未查询到二维表数据");
            }
            String tableMeta = table.getTableMeta();
            List<TableMetaJson> tableMetaJson = StringUtils.isEmpty(tableMeta) ? Collections.emptyList() : JSON.parseArray(table.getTableMeta(), TableMetaJson.class);
            MemTableDataVo memTableDataVo = pageInfo.getList().get(0);
            nextEdgeInfoVO.setCurrentNode(currentNode.getProperties().getText());
            // 路线
            List<FlowChart.EdgeDTO> edgeS = FlowChartUtils.getEdgesBySourceId(flowChart, currentNode.getId());
            Iterator<FlowChart.EdgeDTO> iterator = edgeS.iterator();
            while (iterator.hasNext()) {
                FlowChart.EdgeDTO edge = iterator.next();
                if (edge.getProperties() != null && StringUtils.isNotEmpty(edge.getProperties().getVisibleCondition())) {
                    String visibleCondition = edge.getProperties().getVisibleCondition();
                    if (!ruleService.validateVisibleCondition(tableId, dataId, ruleService.handleRuleExpress(visibleCondition))) {
                        iterator.remove();
                    }
                }
            }
            Map<String, FlowChart.EdgeDTO> edgeIdMap = edgeS.stream().collect(Collectors.toMap(FlowChart.EdgeDTO::getId, Function.identity()));
            Map<String, FlowChart.NodeDTO> map = FlowChartUtils.getTargetNodesByEdges(flowChart, edgeS);
            List<SubmitInfoEdgeVO> submitEdge = nextEdgeInfoVO.getSubmitEdge();
            for (Map.Entry<String, FlowChart.NodeDTO> entry : map.entrySet()) {
                FlowChart.NodeDTO value = entry.getValue();
                SubmitInfoEdgeVO submitInfoEdgeVO = new SubmitInfoEdgeVO();
                if (value.getType() == FlowNodeTypeEnums.TASK) {
                    FlowChart.NodeDTO.PropertiesDTO nodeProperties = value.getProperties();
                    String unitColumnVal = "";
                    String unitColumnCode = nodeProperties.getUnitColumnCode();
                    int unitColumn = 0;
                    boolean unitColumnFlag = false;
                    for (TableMetaJson tableMetaJsonItem : tableMetaJson) {
                        if (StringUtils.equals(tableMetaJsonItem.getCode(), unitColumnCode)) {
                            unitColumnFlag = true;
                            break;
                        }
                        unitColumn++;
                    }
                    if (!unitColumnFlag) {
                        throw new WfRuntimeException("未找到二维表单位列");
                    }
                    for (RowDto rowDto : memTableDataVo.getRows()) {
                        if (rowDto == null || CollectionUtils.isEmpty(rowDto.getCells()) || rowDto.getCells().size() <= unitColumn || rowDto.getCells().get(unitColumn) == null) {
                            throw new WfRuntimeException("未查询到二维表单位列数据");
                        }
                        CellDto cellDto = rowDto.getCells().get(unitColumn);
                        if (StringUtils.isEmpty(unitColumnVal) && cellDto.getOriginalValue() != null) {
                            unitColumnVal = String.valueOf(cellDto.getOriginalValue());
                        } else {
                            if (!StringUtils.equals(unitColumnVal, String.valueOf(cellDto.getOriginalValue()))) {
                                throw new WfRuntimeException("不能提交不同单位的数据");
                            }
                        }
                    }
                    if (StringUtils.isNotEmpty(value.getProperties().getRoleId())) {
                        List<SysUser> sysUsers = sysUserService.selectUserByRoleId(value.getProperties().getRoleId(), unitColumnVal);
                        submitInfoEdgeVO.setNextNodeUser(sysUsers.stream().map(SysUser::getNickName).collect(Collectors.joining(",")));
                    }
                }
                FlowChart.EdgeDTO object = MapUtils.getObject(edgeIdMap, entry.getKey());
                if (object != null) {
                    BeanUtils.copyProperties(object, submitInfoEdgeVO);
                    if (object.getText() != null) {
                        submitInfoEdgeVO.setText(object.getText().getValue());
                    }
                }
                submitEdge.add(submitInfoEdgeVO);
            }
        } else {
            WfTask wfTask = this.getById(taskId);
            if (wfTask == null) {
                throw new WfRuntimeException("未查询到任务");
            }
            String nodeId = wfTask.getNodeId();
            WfVariable wfVariable = wfVariableService.getOne(Wrappers.<WfVariable>lambdaQuery()
                    .eq(WfVariable::getInstanceId, wfTask.getInstanceId())
                    .eq(WfVariable::getNodeId, nodeId)
                    .eq(WfVariable::getVariableKey, WfRtConstants.WfVariableKey.PARENT_NODE_ID)
                    .last("limit 1")
            );
            if (wfVariable == null || StringUtils.isEmpty(wfVariable.getVariableVal()) || StringUtils.equals(wfVariable.getVariableVal(), "0")) {
                nextEdgeInfoVO.setNextNodeName("流程结束");
            } else {
                DimInstance dimInstance = dimInstanceService.getById(wfVariable.getVariableVal());
                if (dimInstance == null) {
                    throw new WfRuntimeException("未查询到维度父节点");
                }
                nextEdgeInfoVO.setNextNodeName(dimInstance.getDimName());
            }
        }
        return nextEdgeInfoVO;
    }

    @Override
    public void executeWFComputeRule(Long tableId, String dataId, FlowChart.EdgeDTO currentEdge) {
        if (tableId == null || StringUtils.isEmpty(dataId) || currentEdge == null || currentEdge.getProperties() == null || StringUtils.isEmpty(currentEdge.getProperties().getExecuteOperation())) {
            return;
        }
        ruleService.executeWFComputeRule(Math.toIntExact(tableId), dataId, currentEdge.getProperties().getExecuteOperation());
    }

    @Override
    public List<WfTaskVO> childTaskList(WfTaskDTO wfTaskDto) {
        WfTask wfTask = this.getById(wfTaskDto.getId());
        Assert.notNull(wfTask, "未查询到任务");
        Long defineId = wfTask.getDefineId();
        WfDefine wfDefine = wfDefineService.getById(defineId);
        Assert.notNull(wfDefine, "未查询到流程定义");
        String flowStateColumnCode = wfDefine.getFlowStateColumnCode();
        List<DimInstance> dimInstanceList = dimInstanceService.list(Wrappers.<DimInstance>lambdaQuery()
                .eq(DimInstance::getDimDirectoryId, flowStateColumnCode)
                .eq(DimInstance::getParentId, wfTask.getNodeId())
        );
        if (CollectionUtils.isEmpty(dimInstanceList)) {
            return Collections.emptyList();
        }
        Map<String, String> dimInstanceIdMap = StreamUtils.toMap(dimInstanceList, v -> String.valueOf(v.getId()), DimInstance::getDimName);
        List<WfDimDefineDetailVO> wfDimDefineDetailVOS = wfDefineService.getByDefineId(defineId, dimInstanceIdMap.keySet());
        if (CollectionUtils.isEmpty(wfDimDefineDetailVOS)) {
            return Collections.emptyList();
        }
        Set<String> dimIdSet = StreamUtils.toSet(wfDimDefineDetailVOS, v -> String.valueOf(v.getDimId()));
        List<WfTask> wfTaskList = this.list(Wrappers.<WfTask>lambdaQuery()
                .eq(WfTask::getInstanceId, wfTask.getInstanceId())
                .in(WfTask::getNodeId, dimIdSet)
        );
        if (CollectionUtils.isEmpty(wfTaskList)) {
            return Collections.emptyList();
        }
        List<WfTaskVO> wfTaskVOS = new ArrayList<>(dimIdSet.size());
        // 归口节点审批
        List<WfVariable> wfTaskStateList = wfVariableService.list(Wrappers.<WfVariable>lambdaQuery()
                .eq(WfVariable::getInstanceId, wfTask.getInstanceId())
                .in(WfVariable::getNodeId, dimIdSet)
                .eq(WfVariable::getVariableKey, WfRtConstants.WfVariableKey.TASK_STATE)
        );
        Map<String, String> wfTaskStateMap = Maps.newHashMapWithExpectedSize(dimIdSet.size());
        Map<String, Map<String, String>> wfCentralizeTaskStateMap = Maps.newHashMap();
        for (WfVariable wfVariable : wfTaskStateList) {
            if (StringUtils.isNotEmpty(wfVariable.getCentralizeId())) {
                Map<String, String> map = wfCentralizeTaskStateMap.computeIfAbsent(wfVariable.getNodeId(), k -> Maps.newHashMap());
                map.put(wfVariable.getCentralizeId(), wfVariable.getVariableVal());
            } else {
                wfTaskStateMap.put(wfVariable.getNodeId(), wfVariable.getVariableVal());
            }
        }
        boolean centralizeFlag = wfTask.getCentralizeDimDefineId() != null;
        Map<String, List<WfTask>> wfTaskNodeIdMap = StreamUtils.groupByKey(wfTaskList, WfTask::getNodeId);
        for (Map.Entry<String, List<WfTask>> entry : wfTaskNodeIdMap.entrySet()) {
            WfTaskStateEnums wfTaskStateEnums = WfTaskStateEnums.getByName(MapUtils.getString(wfTaskStateMap, entry.getKey()));
            Set<String> handledNodeIds = new HashSet<>();
            boolean handleFlag = WfTaskStateEnums.PRE_SUBMIT != wfTaskStateEnums;
            for (WfTask task : entry.getValue()) {
                if (handledNodeIds.contains(task.getNodeId())) {
                    break;
                }
                handledNodeIds.add(task.getNodeId());
                WfTaskVO wfTaskVO = new WfTaskVO();
                wfTaskVO.setId(task.getId());
                wfTaskVO.setNodeId(task.getNodeId());
                wfTaskVO.setNodeName(MapUtils.getString(dimInstanceIdMap, task.getNodeId()));
                if (centralizeFlag && handleFlag) {
                    wfTaskVO.setTaskState(WfTaskStateEnums.getByName(MapUtils.getString(MapUtils.getObject(wfCentralizeTaskStateMap, task.getNodeId()), String.valueOf(wfTask.getCentralizeDimDefineId()))));
                } else {
                    wfTaskVO.setTaskState(wfTaskStateEnums);
                }
                if (wfTaskVO.getTaskState() != null) {
                    wfTaskVO.setTaskStateName(wfTaskVO.getTaskState().getCName());
                }
                wfTaskVO.setCentralizeDimDefineId(task.getCentralizeDimDefineId());
                wfTaskVOS.add(wfTaskVO);
                if (handleFlag) {
                    break;
                }
            }
        }

        return wfTaskVOS;

    }

    @Override
    public List<ViewNodeStateVO> viewNodeState(WfTaskDTO wfTaskDto) {
        Assert.notNull(wfTaskDto.getId(), "任务id不能为空");
        WfTask wfTask = this.getById(wfTaskDto.getId());
        Assert.notNull(wfTask, "未查询到任务");
//        Assert.isTrue(wfTask.getFlowType() == WfInstanceTypeEnums.CUBE, "多维流程才可以查看节点状态");
        if (wfTask.getFlowType() == WfInstanceTypeEnums.CUBE) {
            // 是否查看归口审批
            boolean centralizeFlag = StringUtils.isNotEmpty(wfTaskDto.getNodeId());
            Long defineId = wfTask.getDefineId();
            WfDefine wfDefine = wfDefineService.getById(defineId);
            Assert.notNull(wfDefine, "未查询到流程定义");
            String flowStateColumnCode = wfDefine.getFlowStateColumnCode();
            DimInstance dimInstance = dimInstanceService.getById(wfTask.getNodeId());
            Assert.notNull(dimInstance, "未查询到维度");
            // 查询维度
            String[] dimAncestors = dimInstance.getAncestors().split(",");
            LambdaQueryWrapper<DimInstance> dimInstanceLambdaQueryWrapper = Wrappers.lambdaQuery();
            dimInstanceLambdaQueryWrapper.eq(DimInstance::getDimDirectoryId, flowStateColumnCode);
            // 查看自己所有下级节点
            if (centralizeFlag) {
                // 查询父节点id
                WfVariable wfVariable = wfVariableService.getOne(Wrappers.<WfVariable>lambdaQuery()
                        .eq(WfVariable::getInstanceId, wfTask.getInstanceId())
                        .eq(WfVariable::getNodeId, wfTaskDto.getNodeId())
                        .eq(WfVariable::getVariableKey, WfRtConstants.WfVariableKey.PARENT_NODE_ID)
                        .last("limit 1")
                );
                if (wfVariable == null || StringUtils.isEmpty(wfVariable.getVariableVal()) || StringUtils.equals(wfVariable.getVariableVal(), "0")) {
                    dimInstanceLambdaQueryWrapper.eq(DimInstance::getId, wfTaskDto.getNodeId());
                } else {
                    dimInstanceLambdaQueryWrapper.in(DimInstance::getId, wfTaskDto.getNodeId(), wfVariable.getVariableVal());
                }
            } else {
                // 第一级
                int length = dimAncestors.length;
                if (length == 2) {
                    dimInstanceLambdaQueryWrapper.likeRight(DimInstance::getAncestors, dimInstance.getAncestors());
                } else if (length == 3) {
                    dimInstanceLambdaQueryWrapper.and(wrapper ->
                            wrapper.in(DimInstance::getId, dimAncestors[length - 2])
                                    .or()
                                    .likeRight(DimInstance::getAncestors, dimInstance.getAncestors()));
                } else if (length > 3) {
                    dimInstanceLambdaQueryWrapper.and(wrapper ->
                            wrapper.in(DimInstance::getId, dimAncestors[length - 3], dimAncestors[length - 2])
                                    .or()
                                    .likeRight(DimInstance::getAncestors, dimInstance.getAncestors()));
                }
            }
            List<DimInstance> dimInstanceList = dimInstanceService.list(dimInstanceLambdaQueryWrapper);
            if (CollectionUtils.isEmpty(dimInstanceList)) {
                return Collections.emptyList();
            }
            Map<String, String> dimInstanceIdMap = StreamUtils.toMap(dimInstanceList, v -> String.valueOf(v.getId()), DimInstance::getDimName);
            List<WfDimDefineDetailVO> wfDimDefineDetailVOS = wfDefineService.getByDefineId(defineId, dimInstanceIdMap.keySet());
            if (CollectionUtils.isEmpty(wfDimDefineDetailVOS)) {
                return Collections.emptyList();
            }
            Set<String> dimIdSet = StreamUtils.toSet(wfDimDefineDetailVOS, v -> String.valueOf(v.getDimId()));
            List<WfTask> wfTaskList = this.list(Wrappers.<WfTask>lambdaQuery()
                    .eq(WfTask::getInstanceId, wfTask.getInstanceId())
                    .in(WfTask::getNodeId, dimIdSet)
            );
            List<WfVariable> wfTaskStateList = wfVariableService.list(Wrappers.<WfVariable>lambdaQuery()
                    .eq(WfVariable::getInstanceId, wfTask.getInstanceId())
                    .in(WfVariable::getNodeId, dimIdSet)
                    .eq(WfVariable::getVariableKey, WfRtConstants.WfVariableKey.TASK_STATE)
            );
            Map<String, String> wfTaskStateMap = Maps.newHashMapWithExpectedSize(dimIdSet.size());
            Map<String, Map<String, String>> wfCentralizeTaskStateMap = Maps.newHashMap();
            for (WfVariable wfVariable : wfTaskStateList) {
                if (StringUtils.isEmpty(wfVariable.getCentralizeId())) {
                    wfTaskStateMap.put(wfVariable.getNodeId(), wfVariable.getVariableVal());
                } else {
                    Map<String, String> centralizeTaskStateMap = wfCentralizeTaskStateMap.computeIfAbsent(wfVariable.getNodeId(), k -> Maps.newHashMap());
                    centralizeTaskStateMap.put(wfVariable.getCentralizeId(), wfVariable.getVariableVal());
                }
            }
            Map<String, List<WfTask>> nodeIdTaskListMap = StreamUtils.groupByKey(StreamUtils.filter(wfTaskList, v -> v.getCentralizeDimDefineId() == null), WfTask::getNodeId);
            Map<Long, List<WfDimDefineCentralize>> dimCentralizeMap = Maps.newHashMapWithExpectedSize(wfDimDefineDetailVOS.size());
            // 拿到所有的归口维度
            Set<Long> wfDimDefineDetailIds = wfDimDefineDetailVOS.stream()
                    .filter(v -> StringUtils.isNotEmpty(v.getCentralizedDimId()))
                    .map(WfDimDefineDetailVO::getId)
                    .collect(Collectors.toSet());
            List<WfDimDefineCentralize> centralizes;
            if (CollectionUtils.isNotEmpty(wfDimDefineDetailIds)) {
                centralizes = wfDimDefineDetailService.getCentralizes(wfDimDefineDetailIds);
                if (centralizeFlag) {
                    Set<Long> userIds = new HashSet<>();
                    for (WfDimDefineCentralize wfDimDefineCentralize : centralizes) {
                        if (StringUtils.isNotEmpty(wfDimDefineCentralize.getUserId())) {
                            List<String> userIdList = Arrays.asList(wfDimDefineCentralize.getUserId().split(","));
                            wfDimDefineCentralize.setUserIds(userIdList);
                            userIds.addAll(userIdList.stream().map(Long::valueOf).collect(Collectors.toSet()));
                        }
                    }
                    Map<Long, String> userIdMap;
                    if (CollectionUtils.isNotEmpty(userIds)) {
                        List<SysUser> sysUsers = sysUserService.listByIds(userIds);
                        userIdMap = StreamUtils.toMap(sysUsers, SysUser::getUserId, v -> v.getNickName() + "(" + v.getUserName() + ")");
                    } else {
                        userIdMap = Collections.emptyMap();
                    }
                    centralizes.forEach(wfDimDefineCentralize -> {
                        if (CollectionUtils.isNotEmpty(wfDimDefineCentralize.getUserIds())) {
                            wfDimDefineCentralize.setUserName(wfDimDefineCentralize.getUserIds().stream().map(v -> MapUtils.getObject(userIdMap, Long.valueOf(v))).collect(Collectors.joining(",")));
                        }
                    });
                }
            } else {
                centralizes = Collections.emptyList();
            }
            //设置归口维度属性
            if (CollectionUtils.isNotEmpty(centralizes)) {
                Map<Long, List<WfDimDefineCentralize>> longListMap = StreamUtils.groupByKey(centralizes, WfDimDefineCentralize::getDimDefineDetailId);
                wfDimDefineDetailVOS.forEach(wfDimDefineDetail -> wfDimDefineDetail.setCentralizeList(MapUtils.getObject(longListMap, wfDimDefineDetail.getId())));
            }
            for (WfDimDefineDetailVO dimDefineDetail : wfDimDefineDetailVOS) {
                if (CollectionUtils.isNotEmpty(dimDefineDetail.getCentralizeList())) {
                    dimCentralizeMap.put(dimDefineDetail.getDimId(), dimDefineDetail.getCentralizeList());
                }
            }
            if (centralizeFlag) {
                wfDimDefineDetailVOS.removeIf(v -> !StringUtils.equals(String.valueOf(v.getDimId()), wfTaskDto.getNodeId()));
            } else {
                wfDimDefineDetailVOS.removeIf(v -> !StringUtils.startsWith(String.valueOf(v.getAncestors()), dimInstance.getAncestors()));
            }
            return wfDimDefineDetailVOS.stream()
                    .map(wfDimDefineDetailVO -> {
                        ViewNodeStateVO viewNodeStateVO = new ViewNodeStateVO();
                        String nodeId = String.valueOf(wfDimDefineDetailVO.getDimId());
                        viewNodeStateVO.setNodeId(nodeId);
                        viewNodeStateVO.setNodeName(MapUtils.getString(dimInstanceIdMap, nodeId));
//                viewNodeStateVO.setInstanceId(wfTask.getInstanceId());
//                viewNodeStateVO.setTaskId(wfTask.getId());
                        WfTaskStateEnums wfTaskStateEnums = WfTaskStateEnums.getByName(MapUtils.getObject(wfTaskStateMap, nodeId));
                        if (wfTaskStateEnums != null) {
                            viewNodeStateVO.setTaskState(wfTaskStateEnums);
                            viewNodeStateVO.setTaskStateName(wfTaskStateEnums.getCName());
                        }
                        List<WfTask> wfTasks = MapUtils.getObject(nodeIdTaskListMap, nodeId);
                        if (CollectionUtils.isNotEmpty(wfTasks)) {
                            viewNodeStateVO.setUserName(StreamUtils.join(wfTasks, WfTask::getUserName));
                        }
                        viewNodeStateVO.setAncestors(wfDimDefineDetailVO.getAncestors());
                        viewNodeStateVO.setIsLeaf(wfDimDefineDetailVO.getIsLeaf());
                        viewNodeStateVO.setParentId(wfDimDefineDetailVO.getParentId());
                        List<WfDimDefineCentralize> wfDimDefineCentralizes = MapUtils.getObject(dimCentralizeMap, viewNodeStateVO.getParentId());
                        if (CollectionUtils.isNotEmpty(wfDimDefineCentralizes)) {
//                    if (centralizeFlag) {
//                        wfDimDefineCentralizes.removeIf(v -> !Objects.equals(v.getId(), wfTaskDto.getCentralizeDimDefineId()));
//                    }
                            for (WfDimDefineCentralize wfDimDefineCentralize : wfDimDefineCentralizes) {
                                String taskState = MapUtils.getString(MapUtils.getObject(wfCentralizeTaskStateMap, nodeId), String.valueOf(wfDimDefineCentralize.getId()));
                                WfTaskStateEnums taskStateEnums = WfTaskStateEnums.getByName(taskState);
                                if (taskStateEnums != null) {
                                    wfDimDefineCentralize.setTaskStateName(taskStateEnums.getCName());
                                }
                                wfDimDefineCentralize.setNodeId(nodeId);
                            }
                            viewNodeStateVO.setCentralizeList(wfDimDefineCentralizes);
                        }
                        return viewNodeStateVO;
                    })
                    .collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

}
