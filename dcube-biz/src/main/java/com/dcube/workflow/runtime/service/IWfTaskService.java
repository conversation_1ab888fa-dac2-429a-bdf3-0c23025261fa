package com.dcube.workflow.runtime.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.biz.dto.RowDto;
import com.dcube.workflow.define.flowchart.FlowChart;
import com.dcube.workflow.runtime.constants.enums.WfInstanceTypeEnums;
import com.dcube.workflow.runtime.domain.WfInstance;
import com.dcube.workflow.runtime.domain.WfTask;
import com.dcube.workflow.runtime.dto.WfTaskDTO;
import com.dcube.workflow.runtime.vo.NextEdgeInfoVO;
import com.dcube.workflow.runtime.vo.ViewNodeStateVO;
import com.dcube.workflow.runtime.vo.WfTaskVO;

import java.util.List;
import java.util.Map;

/**
 * 流程任务Service接口
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
public interface IWfTaskService extends IService<WfTask> {
    /**
     * 查询流程任务
     *
     * @param id 流程任务主键
     * @return 流程任务
     */
    WfTask selectCubeWfTaskById(Long id);

    /**
     * 查询用户流程任务数量
     *
     * @param userId 流程任务
     * @return 流程任务数量
     */
    Map<String, Object> selectWfTaskCountByUserId(String userId);

    /**
     * 查询流程任务列表
     *
     * @param wfTask 流程任务
     * @return 流程任务集合
     */
    List<WfTaskVO> selectCubeWfTaskList(WfTaskDTO wfTask);

    /**
     * 新增流程任务
     *
     * @param wfTask 流程任务
     * @return 结果
     */
    int insertCubeWfTask(WfTask wfTask);

    /**
     * 修改流程任务
     *
     * @param wfTask 流程任务
     * @return 结果
     */
    int updateCubeWfTask(WfTask wfTask);

    /**
     * 批量删除流程任务
     *
     * @param ids 需要删除的流程任务主键集合
     * @return 结果
     */
    int deleteCubeWfTaskByIds(Long[] ids);

    /**
     * 删除流程任务信息
     *
     * @param id 流程任务主键
     * @return 结果
     */
    int deleteCubeWfTaskById(Long id);

    WfTask createTask(WfTask wfTask);

    WfTask handle(WfTaskDTO wfTaskDto);

    WfTask handle(Long id,
                  String nextEdgeId,
                  String nextEdgeName,
                  String nextNodeId,
                  String nextNodeName,
                  String commentContent,
                  boolean startProcess);

    WfTask handle(WfTask wfTask);

    void enterNextNode(WfInstance wfInstance,
                       FlowChart.NodeDTO nextNode,
                       String memTableName,
                       String childFlag,
                       Integer tableLevel,
                       List<RowDto> rows,
                       String deptName,
                       int wfStateColumnCodeIndex);

    NextEdgeInfoVO nextEdgeInfo(Integer tableId, Long taskId, String dataId, WfInstanceTypeEnums flowType);

    void executeWFComputeRule(Long tableId, String dataId, FlowChart.EdgeDTO currentEdge);

    List<WfTaskVO> childTaskList(WfTaskDTO wfTaskDto);

    List<ViewNodeStateVO> viewNodeState(WfTaskDTO wfTaskDto);
}
