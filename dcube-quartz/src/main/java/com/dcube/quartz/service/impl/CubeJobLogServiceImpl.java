package com.dcube.quartz.service.impl;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.quartz.dto.PageJobLogDto;
import com.dcube.quartz.mapper.CubeJobLogMapper;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dcube.quartz.domain.CubeJobLog;
import com.dcube.quartz.service.ICubeJobLogService;

/**
 * 定时任务调度日志信息 服务层
 *
 * <AUTHOR>
 */
@Service
public class CubeJobLogServiceImpl extends ServiceImpl<CubeJobLogMapper, CubeJobLog> implements ICubeJobLogService {
    @Autowired
    private CubeJobLogMapper jobLogMapper;

    /**
     * 获取quartz调度器日志的计划任务
     *
     * @param jobLog 调度日志信息
     * @return 调度任务日志集合
     */
    @Override
    public List<CubeJobLog> selectJobLogList(CubeJobLog jobLog) {
        return jobLogMapper.selectJobLogList(jobLog);
    }

    /**
     * 通过调度任务日志ID查询调度信息
     *
     * @param jobLogId 调度任务日志ID
     * @return 调度任务日志对象信息
     */
    @Override
    public CubeJobLog selectJobLogById(Long jobLogId) {
        return jobLogMapper.selectJobLogById(jobLogId);
    }

    /**
     * 新增任务日志
     *
     * @param jobLog 调度日志信息
     */
    @Override
    public void addJobLog(CubeJobLog jobLog) {
        jobLogMapper.insertJobLog(jobLog);
    }

    /**
     * 批量删除调度日志信息
     *
     * @param logIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteJobLogByIds(Long[] logIds) {
        return jobLogMapper.deleteJobLogByIds(logIds);
    }

    /**
     * 删除任务日志
     *
     * @param jobId 调度日志ID
     */
    @Override
    public int deleteJobLogById(Long jobId) {
        return jobLogMapper.deleteJobLogById(jobId);
    }

    /**
     * 清空任务日志
     */
    @Override
    public void cleanJobLog() {
        jobLogMapper.cleanJobLog();
    }

    @Override
    public List<CubeJobLog> pageJobLog(PageJobLogDto pageJobLogDto) {
        CubeJobLog cubeJobLog = new CubeJobLog();
        cubeJobLog.setJobId(pageJobLogDto.getJobId());
        Map<String, Object> param = Maps.newHashMapWithExpectedSize(2);
        param.put("beginTime", pageJobLogDto.getBeginTime());
        param.put("endTime", pageJobLogDto.getEndTime());
        cubeJobLog.setParams(param);
        return this.selectJobLogList(cubeJobLog);
    }
}
