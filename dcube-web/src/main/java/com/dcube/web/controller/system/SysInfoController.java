package com.dcube.web.controller.system;

import cn.hutool.core.bean.BeanUtil;
import com.dcube.common.annotation.Anonymous;
import com.dcube.common.config.DCubeConfig;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.vo.DCubeConfigVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "系统信息")
@RestController
@RequestMapping("/system/info")
@Anonymous
public class SysInfoController {
    /** 系统基础配置 */
    @Autowired
    private DCubeConfig dCubeConfig;

    /**
     * 系统版本
     */
    @Deprecated
    @Operation(summary = "系统版本")
    @GetMapping("/version")
    public AjaxResult index() {
        return AjaxResult.success(dCubeConfig.getVersion());
    }

    /**
     * 全部系统信息
     */
    @Operation(summary = "全部系统信息")
    @GetMapping("")
    public AjaxResult userDefaultPassWord() {
        return AjaxResult.success(BeanUtil.copyProperties(dCubeConfig, DCubeConfigVO.class));
    }

}
