package com.dcube.web.controller.system;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.dcube.common.annotation.Anonymous;
import com.dcube.common.annotation.Log;
import com.dcube.common.constant.UserConstants;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.core.domain.TreeSelect;
import com.dcube.common.core.domain.entity.SysDept;
import com.dcube.common.core.vo.MasterDimDeptVO;
import com.dcube.common.dto.DeptData;
import com.dcube.common.enums.BusinessType;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.file.FileUploadUtils;
import com.dcube.system.service.ISysDeptService;
import com.google.common.collect.Maps;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 部门信息
 *
 * <AUTHOR>
 */
@Tag(name = "部门信息")
@RestController
@RequestMapping("/system/dept")
@Slf4j
public class SysDeptController extends BaseController {
    @Autowired
    private ISysDeptService deptService;

    /**
     * 获取部门列表
     */
    @Operation(summary = "部门列表")
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/list")
    public AjaxResult list(SysDept dept) {
        List<SysDept> depts = deptService.selectDeptList(dept);
        return success(depts);
    }

    @Operation(summary = "部门列表树")
    @GetMapping("/tree")
    public AjaxResult tree(SysDept dept) {
        List<TreeSelect> depts = deptService.selectDeptTreeList(dept);
        return success(depts);
    }

    /**
     * 查询部门列表（排除节点）
     */
    @Operation(summary = "根据部门id获取部门信息（排除节点）")
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/list/exclude/{deptId}")
    public AjaxResult excludeChild(@PathVariable(value = "deptId", required = false) Long deptId) {
        List<SysDept> depts = deptService.selectDeptList(new SysDept());
        depts.removeIf(d -> d.getDeptId().intValue() == deptId || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), deptId + ""));
        return success(depts);
    }

    /**
     * 根据部门编号获取详细信息
     */
    @Operation(summary = "根据部门id获取部门信息")
    @PreAuthorize("@ss.hasPermi('system:dept:query')")
    @GetMapping(value = "/{deptId}")
    public AjaxResult getInfo(@PathVariable Long deptId) {
        deptService.checkDeptDataScope(deptId);
        return success(deptService.selectDeptById(deptId));
    }

    /**
     * 新增部门
     */
    @Operation(summary = "添加部门")
    @PreAuthorize("@ss.hasPermi('system:dept:add')")
    @Log(title = "部门管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysDept dept) {
        if (UserConstants.NOT_UNIQUE.equals(deptService.checkDeptNameUnique(dept))) {
            return error("新增部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        }
        dept.setCreateBy(getUsername());
        return toAjax(deptService.insertDept(dept));
    }

    /**
     * 修改部门
     */
    @Operation(summary = "编辑部门")
    @PreAuthorize("@ss.hasPermi('system:dept:edit')")
    @Log(title = "部门管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysDept dept) {
        Long deptId = dept.getDeptId();
        deptService.checkDeptDataScope(deptId);
        if (UserConstants.NOT_UNIQUE.equals(deptService.checkDeptNameUnique(dept))) {
            return error("修改部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        } else if (dept.getParentId().equals(deptId)) {
            return error("修改部门'" + dept.getDeptName() + "'失败，上级部门不能是自己");
        } else if (StringUtils.equals(UserConstants.DEPT_DISABLE, dept.getStatus()) && deptService.selectNormalChildrenDeptById(deptId) > 0) {
            return error("该部门包含未停用的子部门！");
        }
        dept.setUpdateBy(getUsername());
        return toAjax(deptService.updateDept(dept));
    }

    /**
     * 删除部门
     */
    @Operation(summary = "删除部门")
    @PreAuthorize("@ss.hasPermi('system:dept:remove')")
    @Log(title = "部门管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deptId}")
    public AjaxResult remove(@PathVariable Long deptId) {
        if (deptService.hasChildByDeptId(deptId)) {
            return warn("存在下级部门,不允许删除");
        }
        if (deptService.checkDeptExistUser(deptId)) {
            return warn("部门存在用户,不允许删除");
        }
        deptService.checkDeptDataScope(deptId);
        return toAjax(deptService.deleteDeptById(deptId));
    }

    /**
     * 下载部门信息模板
     */
    @Operation(summary = "下载部门信息模板")
    @GetMapping("/downloadTemplate")
    @Anonymous
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("机构模板", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 这里需要设置不关闭流

            EasyExcel.write(response.getOutputStream(), DeptData.class)
                    .autoCloseStream(Boolean.FALSE)
                    .sheet("模板")
                    .doWrite(() -> {
                        List<DeptData> list = new ArrayList<>(2);
                        DeptData deptData = new DeptData();
                        deptData.setDeptName("企算银行");
                        deptData.setDeptType("后台");
                        deptData.setParentDeptName("");
                        deptData.setDeptLevel("0");
                        list.add(deptData);
                        DeptData deptData2 = new DeptData();
                        deptData2.setDeptName("北京分行");
                        deptData2.setDeptType("前台");
                        deptData2.setParentDeptName("企算银行");
                        deptData2.setDeptLevel("1");
                        list.add(deptData2);
                        return list;
                    });
        } catch (Exception e) {
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            Map<String, String> map = Maps.newHashMapWithExpectedSize(2);
            map.put("status", "failure");
            map.put("message", "下载文件失败" + e.getMessage());
            response.getWriter().println(JSON.toJSONString(map));
        }
    }

    /**
     * 从excel导入部门信息模板
     */
    @Operation(summary = "从excel导入部门信息模板")
    @PostMapping("/importFromExcel")
    public AjaxResult importFromExcel(@RequestPart("file") MultipartFile file) throws IOException {
        FileUploadUtils.validateUploadFile(file, FileUploadUtils.FileType.EXCEL);
        deptService.importFromExcel(file);
        return AjaxResult.success("导入成功");
    }

    /**
     * 主数据维度获取部门列表
     */
    @Operation(summary = "主数据维度获取部门列表（不带权限校验、带数据权限）")
    @GetMapping("/masterDimGetDeptList")
    public AjaxResult masterDimGetDeptList(SysDept dept) {
        List<MasterDimDeptVO> depts = deptService.masterDimGetDeptList(dept);
        return success(depts);
    }

}
