package com.dcube.web.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;

/**
 * @创建人 zhouhx
 * @创建时间 2023/11/30 16:26
 * @描述
 */
@Slf4j
@Configuration
public class WebSocketConfig {

    @Bean
    @ConditionalOnProperty(prefix = "server.websocket.exporter", name = "enable", havingValue = "true")
    public ServerEndpointExporter serverEndpointExporter() {
        log.info("websocket 服务器启动");
        return new ServerEndpointExporter();
    }

    @Bean
    @ConditionalOnProperty(prefix = "server.websocket.exporter", name = "enable", havingValue = "true")
    public ServletServerContainerFactoryBean createWebSocketContainer() {
        log.info("websocket 创建容器");
        ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
        // 在此处设置bufferSize
        container.setMaxTextMessageBufferSize(512000);
        container.setMaxBinaryMessageBufferSize(512000);
        container.setMaxSessionIdleTimeout(15 * 60000L);
        return container;
    }
}
