#不做收缩(不删除注释以及未被引用的代码)
-dontshrink
#不做优化(不变更代码实现逻辑)
-dontoptimize

#-dontobfuscate
#-microedition
#不混忽略非公用类文件及成员
-dontskipnonpubliclibraryclasses
-dontskipnonpubliclibraryclassmembers

#保持目录结构
-keepdirectories
#不混淆所有包名
#-keeppackagenames
#混淆类名之后，对使用Class.forName('className')之类的地方进行相应替代
-adaptclassstrings

#此选项将保存所有软件包中的所有原始接口文件（不进行混淆） 
-keepnames interface ** { *; }
#此选项将保存所有软件包中的所有原始接口文件（不进行混淆） 
-keep interface * extends * { *; }

#保留参数名，因为控制器，或者Mybatis等接口的参数如果混淆会导致无法接受参数，xml文件找不到参数 
-keepparameternames
#保留枚举成员及方法 
-keepclassmembers enum * { *; }
#不混淆所有类,保存原始定义的注释
#spring 相关的注解，不要混淆
-keepclassmembers class * {
    @org.springframework.context.annotation.Bean *;
    @org.springframework.beans.factory.annotation.Autowired *;
    @org.springframework.beans.factory.annotation.Value *;
    @org.springframework.stereotype.Service *;
    @org.springframework.stereotype.Component *;
    @org.springframework.web.bind.annotation.RestController *;
    @org.springframework.** *;
    @org.springframework.beans.factory.annotation.Autowired <fields>;
    @org.springframework.beans.factory.annotation.Autowired <methods>;
    @javax.annotation.PostConstruct *;
    @javax.annotation.PreDestroy *;
    @javax.annotation.Resource *;
    @org.springframework.scheduling.annotation.Async <methods>;
    @org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration *;
    @org.springframework.boot.context.properties.ConfigurationProperties *;
    @org.springframework.beans.factory.annotation.Qualifier *;
    @io.swagger.annotations.ApiParam *;
    @org.springframework.validation.annotation.Validated *;
    @io.swagger.annotations.ApiModelProperty *;
    @javax.validation.constraints.NotNull *;
    @javax.validation.constraints.Size *;
    @javax.validation.constraints.NotBlank *;
    @javax.validation.constraints.Pattern *;
}


#忽略warn消息  
-ignorewarnings

#忽略note消息  
-dontnote

#打印配置信息  
-printconfiguration


#This option will save all original names in interfaces (without obfuscate).
-keepnames interface **

#This option will save all original class files (without obfuscate) but obfuscate all in domain and service packages.
-keep
    class com.dcube.DCubeApplication {
    public static void main(java.lang.String[]);
    }


#This option will save all original class files (without obfuscate) in service package
#-keep class com.slm.proguard.example.spring.boot.service { *; }

#This option will save all original defined annotations in all classes in all packages.
#                       -keep class com.slm.proguard.example.spring.boot.config.BeanConfig
-keep class com.fasterxml.jackson.** { *; }

-keep class org.json.JSONObject.** {** put(java.lang.String,java.util.Map);}


-dontwarn com.fasterxml.jackson.databind.**
-dontwarn com.fasterxml.jackson.**
#不使用大小写混合,混淆后的类名为小写
-dontusemixedcaseclassnames

#使用唯一的类名来混淆
-useuniqueclassmembernames

#允许访问并修改有修饰符的类和类的成员
-allowaccessmodification

#需要保持的属性：异常，内部类，注解等
-keepattributes Exceptions,InnerClass,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,LocalVariable*Table,*Annotation*,Synthetic,EnclosingMethod


# 保留所有 getter/setter 方法（支持链式调用）
-keepclassmembers public class *{
    *** get*();
    void set*(***);
    *** set*(***);  # 额外添加：保留链式 setter
}

# 保留类注解和泛型签名 Lombok
-keepattributes RuntimeVisibleAnnotations, AnnotationDefault

# 保持启动类不变
-keep public class com.dcube.DCubeApplication {*;}


#不混淆被Component等注解标记的类
-keep @org.springframework.stereotype.Component class * {*;}
-keep @org.springframework.stereotype.Service class * {*;}
-keep @org.springframework.web.bind.annotation.RestController class * {*;}
-keep @org.springframework.context.annotation.Configuration class * {*;}
#-keep @org.aspectj.lang.annotation.Aspect class * {*;}


# 配置不混淆某些类
-keep class org.slf4j.** {*;}
#mybatis的mapper/实体类不混淆，否则会导致xml配置的mapper找不到 ( 保持该目录下所有类及其成员不被混淆)
-keep class com.dcube.**.mapper.** {*;}
-keep class com.dcube.**.**.mapper.** {*;}
# 实体类不能混淆
-keep class com.dcube.**.domain.** {*;}
-keep class com.dcube.**.**.domain.** {*;}
-keep class com.dcube.**.vo.** {*;}
-keep class com.dcube.**.**.vo.** {*;}


# Apache POI
-dontwarn org.apache.**
-dontwarn org.openxmlformats.schemas.**
-dontwarn org.etsi.**
-dontwarn org.w3.**
-dontwarn com.microsoft.schemas.**
-dontwarn com.graphbuilder.**
-dontwarn javax.naming.**
-dontwarn java.lang.management.**
-dontwarn org.slf4j.impl.**
-dontwarn java.awt.**
-dontwarn net.sf.saxon.**
-dontwarn org.apache.batik.**
-dontwarn org.apache.logging.log4j.**

-dontnote org.apache.**
-dontnote org.openxmlformats.schemas.**
-dontnote org.etsi.**
-dontnote org.w3.**
-dontnote com.microsoft.schemas.**
-dontnote com.graphbuilder.**
-dontnote javax.naming.**
-dontnote java.lang.management.**
-dontnote org.slf4j.impl.**

-keeppackagenames org.apache.poi.ss.formula.function

-keep,allowoptimization,allowobfuscation class org.apache.logging.log4j.** { *; }
-keep,allowoptimization class org.apache.commons.compress.archivers.zip.** { *; }
-keep,allowoptimization class org.apache.poi.schemas.** { *; }
-keep,allowoptimization class org.apache.xmlbeans.** { *; }
-keep,allowoptimization class org.openxmlformats.schemas.** { *; }
-keep,allowoptimization class com.microsoft.schemas.** { *; }

# langchain4j
-keep class com.alibaba.dashscope.** {*;}
-keep class dev.langchain4j.** {*;}
-keep class lombok.ast.ecj.* { *; }

-keep class io.jsonwebtoken.** { *; }
-keepnames class io.jsonwebtoken.* { *; }
-keepnames interface io.jsonwebtoken.* { *; }

-keep class org.bouncycastle.** { *; }
-keepnames class org.bouncycastle.** { *; }
-dontwarn org.bouncycastle.**

-keep class com.dcube.common.dto.ReportDto { *; }
-keep class com.dcube.rule.cube.vo.CubeRuleVO { *; }
-keep class com.dcube.rule.cube.domain.DimRuleIndicatorRuleRef { *; }
-keep class com.dcube.rule.grid.domain.Rule { *; }
-keep class com.dcube.rule.grid.vo.RuleVO { *; }