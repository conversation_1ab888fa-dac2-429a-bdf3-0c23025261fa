package com.dcube.framework.license.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.dcube.common.enums.BooleanEnum;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.sign.Base64;
import com.dcube.framework.license.service.AbstractServerInfos;
import com.dcube.framework.license.service.LicenseService;
import com.dcube.framework.license.utils.TrialDateValidator;
import com.dcube.framework.license.vo.LicenseCheckVo;
import com.dcube.framework.license.vo.LicenseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Set;

/**
 * @创建人 zhouhx
 * @创建时间 2023/8/24 17:38
 * @描述
 */
@Service
@Slf4j
public class LicenseServiceImpl implements LicenseService {
    private static volatile LicenseVo license;

    @Value("${license.authCode}")
    private String authCode;

    @Value("${license.validate:true}")
    private String validate;

    public LicenseVo getLicenseByAuthCode() {
//        log.info("License authCode:" + authCode);
        if (StringUtils.isEmpty(validate) || BooleanEnum.TRUE.getCode().equals(validate)) {
            if (StringUtils.isEmpty(authCode)) {
                throw new ServiceException("License验证失败：授权码不能为空。");
            }
            if (null == license) {
                synchronized (LicenseVo.class) {
                    if (null == license) {
                        byte[] data = Base64.decode(authCode);
                        if (Objects.nonNull(data) && data.length > 0) {
                            try {
                                license = ObjectUtil.deserialize(data);
                            } catch (Exception e) {
                                log.error("反序列化失败：", e);
                                throw new ServiceException("License验证失败：授权码有误。");
                            }
                        }
                    }
                }
            }
        }
        return license;
    }

    @Override
    public void validateLicense(LicenseVo license) {
        if (StringUtils.isEmpty(validate) || BooleanEnum.TRUE.getCode().equals(validate)) {
            if (Objects.isNull(license)) {
                throw new ServiceException("License验证失败：License信息为空。");
            }
            AbstractServerInfos server = new LinuxServerInfos();
            LicenseCheckVo licenseCheck = server.getServerInfos();
            Set<String> macAddress = licenseCheck.getMacAddress();
            log.info("Mac地址为:{}", macAddress);
            log.info("license信息:{}", license);
            log.info("License有效期为【 {} - {} 】", license.getCustomer().getTrialStartDate(), license.getCustomer().getTrialEndDate());
            if (CollectionUtil.isEmpty(macAddress)) {
                throw new ServiceException("License验证失败：Mac地址获取失败。");
            }
            //验证mac地址
            if (!CollectionUtil.containsAny(macAddress, license.getMacAddress())) {
                throw new ServiceException(String.format("License验证失败：【%s】不是授权的Mac地址。", license.getMacAddress()));
            }
            //验证时间
            if (!TrialDateValidator.validateTrialPeriod(license.getCustomer())) {
                throw new ServiceException(String.format("License验证失败：License已过期，有效期为【 %s - %s 】，请联系管理员续期。", license.getCustomer().getTrialStartDate(), license.getCustomer().getTrialEndDate()));
            }
        }
    }
}
