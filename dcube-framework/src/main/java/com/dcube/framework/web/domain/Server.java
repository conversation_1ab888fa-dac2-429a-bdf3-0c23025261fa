package com.dcube.framework.web.domain;

import com.dcube.common.utils.Arith;
import com.dcube.common.utils.ip.IpUtils;
import com.dcube.framework.web.domain.server.*;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.CentralProcessor.TickType;
import oshi.hardware.GlobalMemory;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.software.os.FileSystem;
import oshi.software.os.OSFileStore;
import oshi.software.os.OperatingSystem;
import oshi.util.Util;

import java.util.ArrayList;
import java.util.List;

public class Server {
    private static final int OSHI_WAIT_SECOND = 1000;
    // 缓存不变的系统属性
    private static final String OS_NAME = System.getProperty("os.name");
    private static final String OS_ARCH = System.getProperty("os.arch");
    private static final String USER_DIR = System.getProperty("user.dir");
    private static final String JAVA_VERSION = System.getProperty("java.version");
    private static final String JAVA_HOME = System.getProperty("java.home");
    // 文件大小转换常量
    private static final long KB = 1024;
    private static final long MB = KB * 1024;
    private static final long GB = MB * 1024;
    /**
     * CPU相关信息
     */
    private final Cpu cpu = new Cpu();
    /**
     * 內存相关信息
     */
    private final Mem mem = new Mem();
    /**
     * JVM相关信息
     */
    private final Jvm jvm = new Jvm();
    /**
     * 服务器相关信息
     */
    private final Sys sys = new Sys();

    /**
     * 磁盘相关信息
     */
    private List<SysFile> sysFiles = new ArrayList<>();

    public Cpu getCpu() {
        return cpu;
    }

    public Mem getMem() {
        return mem;
    }

    public Jvm getJvm() {
        return jvm;
    }

    public Sys getSys() {
        return sys;
    }

    public List<SysFile> getSysFiles() {
        return sysFiles;
    }

    public void copyTo() {
        // 重用SystemInfo实例
        SystemInfo si = new SystemInfo();
        HardwareAbstractionLayer hal = si.getHardware();

        setCpuInfo(hal.getProcessor());

        setMemInfo(hal.getMemory());

        setSysInfo();

        setJvmInfo();

        setSysFiles(si.getOperatingSystem());
    }

    /**
     * 设置CPU信息
     */
    private void setCpuInfo(CentralProcessor processor) {
        // CPU信息
        long[] prevTicks = processor.getSystemCpuLoadTicks();
        Util.sleep(OSHI_WAIT_SECOND);
        long[] ticks = processor.getSystemCpuLoadTicks();
        long nice = ticks[TickType.NICE.getIndex()] - prevTicks[TickType.NICE.getIndex()];
        long irq = ticks[TickType.IRQ.getIndex()] - prevTicks[TickType.IRQ.getIndex()];
        long softirq = ticks[TickType.SOFTIRQ.getIndex()] - prevTicks[TickType.SOFTIRQ.getIndex()];
        long steal = ticks[TickType.STEAL.getIndex()] - prevTicks[TickType.STEAL.getIndex()];
        long cSys = ticks[TickType.SYSTEM.getIndex()] - prevTicks[TickType.SYSTEM.getIndex()];
        long user = ticks[TickType.USER.getIndex()] - prevTicks[TickType.USER.getIndex()];
        long iowait = ticks[TickType.IOWAIT.getIndex()] - prevTicks[TickType.IOWAIT.getIndex()];
        long idle = ticks[TickType.IDLE.getIndex()] - prevTicks[TickType.IDLE.getIndex()];
        long totalCpu = user + nice + cSys + idle + iowait + irq + softirq + steal;
        cpu.setCpuNum(processor.getLogicalProcessorCount());
        cpu.setTotal(totalCpu);
        cpu.setSys(cSys);
        cpu.setUsed(user);
        cpu.setWait(iowait);
        cpu.setFree(idle);
    }

    /**
     * 设置内存信息
     */
    private void setMemInfo(GlobalMemory memory) {
        long total = memory.getTotal();
        long available = memory.getAvailable();

        mem.setTotal(total);
        mem.setUsed(total - available);
        mem.setFree(available);
    }

    /**
     * 设置服务器信息
     */
    private void setSysInfo() {
        sys.setComputerName(IpUtils.getHostName());
        sys.setComputerIp(IpUtils.getHostIp());
        sys.setOsName(OS_NAME);
        sys.setOsArch(OS_ARCH);
        sys.setUserDir(USER_DIR);
    }

    /**
     * 设置Java虚拟机
     */
    private void setJvmInfo() {
        Runtime runtime = Runtime.getRuntime();
        jvm.setTotal(runtime.totalMemory());
        jvm.setMax(runtime.maxMemory());
        jvm.setFree(runtime.freeMemory());
        jvm.setVersion(JAVA_VERSION);
        jvm.setHome(JAVA_HOME);
    }

    /**
     * 设置磁盘信息
     */
    private void setSysFiles(OperatingSystem os) {
        FileSystem fileSystem = os.getFileSystem();
        List<OSFileStore> fsArray = fileSystem.getFileStores();
        sysFiles.clear();
        sysFiles = new ArrayList<>(fsArray.size());
        for (OSFileStore fs : fsArray) {
            long free = fs.getUsableSpace();
            long total = fs.getTotalSpace();
            long used = total - free;
            SysFile sysFile = new SysFile();
            sysFile.setDirName(fs.getMount());
            sysFile.setSysTypeName(fs.getType());
            sysFile.setTypeName(fs.getName());
            sysFile.setTotal(convertFileSize(total));
            sysFile.setFree(convertFileSize(free));
            sysFile.setUsed(convertFileSize(used));
            sysFile.setUsage(Arith.mul(Arith.div(used, total, 4), 100));
            sysFiles.add(sysFile);
        }
    }

    /**
     * 字节转换
     *
     * @param size 字节大小
     * @return 转换后值
     */
    private String convertFileSize(long size) {
        if (size >= GB) {
            return String.format("%.1f GB", (float) size / GB);
        } else if (size >= MB) {
            float f = (float) size / MB;
            // 减少字符串拼接开销
            return (f > 100 ? String.format("%.0f MB", f) : String.format("%.1f MB", f));
        } else if (size >= KB) {
            float f = (float) size / KB;
            return (f > 100 ? String.format("%.0f KB", f) : String.format("%.1f KB", f));
        } else {
            return size + " B";  // 避免使用String.format处理简单拼接
        }
    }
}