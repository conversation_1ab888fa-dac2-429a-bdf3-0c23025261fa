package com.dcube.framework.web.service;

import com.dcube.common.config.DCubeConfig;
import com.dcube.common.core.domain.entity.SysUser;
import com.dcube.common.core.domain.model.LoginUser;
import com.dcube.common.enums.UserStatus;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.SecurityUtils;
import com.dcube.common.utils.StringUtils;
import com.dcube.system.service.IPermissionService;
import com.dcube.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * 用户验证处理
 *
 * <AUTHOR>
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {
    private static final Logger log = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private SysPermissionService sysPermissionService;

    @Autowired
    private IPermissionService permissionService;

    @Autowired
    private DCubeConfig dCubeConfig;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        SysUser user = userService.selectUserByUserName(username);
        if (StringUtils.isNull(user)) {
            log.info("登录用户：{} 不存在.", username);
            throw new ServiceException("登录用户：" + username + " 不存在");
        } else if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            log.info("登录用户：{} 已被删除.", username);
            throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
        } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", username);
            throw new ServiceException("对不起，您的账号：" + username + " 已停用");
        }

        passwordService.validate(user);
        return createLoginUser(user);
    }

    public UserDetails createLoginUser(SysUser user) {
        user.setUseDefaultPassWord(SecurityUtils.matchesPassword(dCubeConfig.getUserDefaultPassWord(), user.getPassword()));
        LoginUser loginUser = new LoginUser(user.getUserId(), user.getDeptId(), user, sysPermissionService.getMenuPermission(user));

        loginUser.setTablePermissions(permissionService.selectUserTableList(user.getUserId(), user.getUserName()));

        return loginUser;
    }
}
