package com.dcube.framework.config;

import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.core.toolkit.Sequence;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.dcube.common.config.properties.SnowflakeProperties;
import com.dcube.common.utils.spring.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.mapping.VendorDatabaseIdProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.net.InetAddress;
import java.util.Properties;

/**
 * Mybatis Plus 配置
 *
 * <AUTHOR>
 */
@EnableTransactionManagement(proxyTargetClass = true)
@Configuration
@Slf4j
public class MybatisPlusConfig {
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 分页插件
        interceptor.addInnerInterceptor(paginationInnerInterceptor());
        // 乐观锁插件
        interceptor.addInnerInterceptor(optimisticLockerInnerInterceptor());
        // 阻断插件
        interceptor.addInnerInterceptor(blockAttackInnerInterceptor());
        return interceptor;
    }

    /**
     * 分页插件，自动识别数据库类型 https://baomidou.com/guide/interceptor-pagination.html
     */
    public PaginationInnerInterceptor paginationInnerInterceptor() {
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor();
        // 设置数据库类型为mysql
        paginationInnerInterceptor.setDbType(DbType.MYSQL);
        // 设置最大单页限制数量，默认 500 条，-1 不受限制
        paginationInnerInterceptor.setMaxLimit(-1L);
        return paginationInnerInterceptor;
    }

    /**
     * 乐观锁插件 https://baomidou.com/guide/interceptor-optimistic-locker.html
     */
    public OptimisticLockerInnerInterceptor optimisticLockerInnerInterceptor() {
        return new OptimisticLockerInnerInterceptor();
    }

    /**
     * 如果是对全表的删除或更新操作，就会终止该操作 https://baomidou.com/guide/interceptor-block-attack.html
     */
    public BlockAttackInnerInterceptor blockAttackInnerInterceptor() {
        return new BlockAttackInnerInterceptor();
    }

    @Bean
    public IdentifierGenerator identifierGenerator() {
        CustomSequence customSequence = new CustomSequence(null);
        log.info("获取到的sequence算法：dataCenterId：{} - workerId：{}", ReflectUtil.getFieldValue(customSequence, "datacenterId"), ReflectUtil.getFieldValue(customSequence, "workerId"));
        return new DefaultIdentifierGenerator(customSequence);
    }

    static class CustomSequence extends Sequence {
        private static final SnowflakeProperties snowflakeProperties = SpringUtils.getBean(SnowflakeProperties.class);

        public CustomSequence(InetAddress inetAddress) {
            super(inetAddress);
        }

        public CustomSequence(long workerId, long datacenterId) {
            super(workerId, datacenterId);
        }

        @Override
        protected long getDatacenterId(long maxDatacenterId) {
            if (snowflakeProperties.getDatacenterId() != null) {
                return snowflakeProperties.getDatacenterId() % (maxDatacenterId + 1);
            }
            return super.getDatacenterId(maxDatacenterId);
        }

        @Override
        protected long getMaxWorkerId(long datacenterId, long maxWorkerId) {
            if (snowflakeProperties.getWorkerId() != null) {
                return snowflakeProperties.getWorkerId() % (maxWorkerId + 1);
            }
            return super.getMaxWorkerId(datacenterId, maxWorkerId);
        }
    }

    @Bean
    public DatabaseIdProvider databaseIdProvider() {
        DatabaseIdProvider databaseIdProvider = new VendorDatabaseIdProvider();
        Properties properties = new Properties();
        properties.setProperty("Oracle", "oracle");
        properties.setProperty("TiDB", "tidb");
        properties.setProperty("DB2", "db2");
        properties.setProperty("SQL Server", "sqlserver");
        properties.setProperty("DM DBMS", "dm");
        properties.setProperty("MySQL", "mysql");
        properties.setProperty("Derby", "derby");
        properties.setProperty("H2", "h2");
        properties.setProperty("HSQL", "hsql");
        properties.setProperty("Informix", "informix");
        properties.setProperty("MS-SQL", "ms-sql");
        properties.setProperty("PostgreSQL", "pg");
        properties.setProperty("GaussDB(DWS)", "pg");
        properties.setProperty("sybase", "sybase");
        properties.setProperty("Hana", "hana");
        databaseIdProvider.setProperties(properties);
        return databaseIdProvider;
    }

}