package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import com.dcube.biz.json.TableMetaJson;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 数据底表
 *
 * <AUTHOR>
 * @date 2025-05-01
 */
@Data
public class DataFoundationTableDto extends BaseDto {

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "表名")
    private String tableName;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "SQL脚本")
    private String sqlScript;

    @Schema(description = "表格元数据")
    private List<TableMetaJson> tableMetaJson;

    @Schema(description = "父Id")
    private Integer parentId;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "祖级列表")
    private String ancestors;

}
