package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.common.utils.poi.Column;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * class description
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Data
public class ViewDto extends BaseDto {

    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "目录ID")
    private String directoryId;

    @Schema(description = "数据源Id，数据源Id必传")
    private String sourceId;

    @Schema(description = "视图名称")
    private String viewName;

    @Schema(description = "SQL脚本，（SQL脚本新增/修改时数据源时必传）")
    private String viewScript;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "数据源类型，（数据源类型新增/修改时数据源时必传）")
    private String sourceType;

    @Schema(description = "数据源配置，（数据源配置新增/修改时数据源时必传）")
    private SourceConfigJson sourceConfig;

    @Schema(description = "视图元数据")
    private List<TableMetaJson> viewMetaList;

    @Schema(description = "Excel文件路径")
    private String filePath;

    @Schema(description = "时间戳")
    private String timestamp;

    @Schema(description = "Excel元数据")
    private List<Column> columns;

    @Schema(description = "公钥")
    private String publicKey;

}
