package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
public class UpdateParentTableMetaDto extends BaseDto {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 父表列序号
     */
    @Schema(description = "父表列序号")
    private Integer parentTableMetaNo;

    /**
     * 父表列编码
     */
    @Schema(description = "父表列编码")
    private String parentTableMetaCode;

    /**
     * 父表列名称
     */
    @Schema(description = "父表列名称")
    private String parentTableMetaName;
}
