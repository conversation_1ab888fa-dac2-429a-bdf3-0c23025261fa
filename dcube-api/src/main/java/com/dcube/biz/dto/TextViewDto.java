package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.biz.json.TableMetaJson;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * class description
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Data
public class TextViewDto extends BaseDto {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 目录ID
     */
    @Schema(description = "目录ID")
    private String directoryId;

    /**
     * 视图名称
     */
    @Schema(description = "视图名称")
    private String viewName;

    @Schema(description = "关系库表名称")
    private String tableName;

    /**
     * SQL脚本
     */
    @Schema(description = "SQL脚本")
    private String viewScript;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    // 数据源
    /**
     * 数据源类型
     */
    @Schema(description = "数据源类型")
    private String sourceType;

    /**
     * 数据源配置
     */
    @Schema(description = "数据源配置")
    private SourceConfigJson sourceConfig;

    /**
     * 视图元数据
     */
    @Schema(description = "视图元数据")
    private List<TableMetaJson> viewMetaList;

    @Schema(description = "数据文件目录")
    private String dataFileDir;

    @Schema(description = "数据文件名称")
    private String fileName;

    @Schema(description = "目录日期格式")
    private String dirDateFormat;

    @Schema(description = "字段分割符")
    private String splitSymbol;
}
