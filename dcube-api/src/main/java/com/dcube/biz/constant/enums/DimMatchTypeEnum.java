package com.dcube.biz.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 匹配方式
 */
@Getter
@AllArgsConstructor
@Schema(description = "匹配方式（名称，顺序）")
public enum DimMatchTypeEnum {
    /**
     * 名称
     */
    DIM_NAME(0, "名称"),
    /**
     * 顺序
     */
    DIM_ORDER(1, "顺序"),

    ;

    @EnumValue
    private final int type;

    private final String desc;

}
