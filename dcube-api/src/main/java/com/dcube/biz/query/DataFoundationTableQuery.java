package com.dcube.biz.query;

import com.dcube.biz.base.PageDto;
import com.dcube.biz.dto.FilterDto;
import com.dcube.biz.dto.SortByDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 数据底表
 *
 * <AUTHOR>
 * @date 2025-05-02
 */
@Data
public class DataFoundationTableQuery extends PageDto {

    private static final long serialVersionUID = 1L;

    @Schema(description = "数据底表ID")
    private Integer tableId;

    @Schema(description = "表名")
    private String tableName;

    @Schema(description = "平均值与求和列")
    private String columnCode;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "数据过滤（AND逻辑处理）")
    private List<FilterDto> filterList;

    @Schema(description = "数据排序")
    private List<SortByDto> sortList;
}
