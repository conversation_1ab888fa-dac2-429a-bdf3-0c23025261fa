package com.dcube.biz.vo;

import com.dcube.biz.dto.LayoutDimDTO;
import com.dcube.biz.dto.LayoutFilterDimDTO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode
@ToString
public class LayoutVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 多维表ID
     */
    @Schema(description = "多维表ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer tableId;

    /**
     * 筛选维度列表
     */
    @Schema(description = "筛选维度对象(包含维度ID和选中维值)集合")
    private List<LayoutFilterDimDTO> filterDimList;

    /**
     * 行维度列表
     */
    @Schema(description = "行维度列表")
    private List<LayoutDimDTO> rowDimList;

    /**
     * 列维度列表
     */
    @Schema(description = "列维度列表")
    private List<LayoutDimDTO> columnDimList;

}
