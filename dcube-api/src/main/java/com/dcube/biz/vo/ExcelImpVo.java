package com.dcube.biz.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @创建人 zhouhx
 * @创建时间 2023/12/5 13:19
 * @描述
 */
@Data
public class ExcelImpVo {

    @Schema(description = "分组Id，导入时必传")
    private Integer parentId;

    @Schema(description = "文件名称数组，导入和删除时必传")
    private String[] fileNames;

    @Schema(description = "用户Id,导入和解压时必传")
    private Long userId;

    @Schema(description = "时间戳")
    private String timestamp;

    @Schema(description = "是否合并")
    private boolean merge;

    @Schema(description = "zip文件名，解压时必传")
    private String fileName;
}
