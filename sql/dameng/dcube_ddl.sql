CREATE TABLE "SYS_ROLE"
(
    "ROLE_ID"             BIGINT IDENTITY(114,1) NOT NULL,
    "ROLE_NAME"           VARCHAR(30)  NOT NULL,
    "ROLE_KEY"            VARCHAR(100) NULL,
    "ROLE_SORT"           INT          NOT NULL,
    "DATA_SCOPE"          CHAR(1)     DEFAULT '1'
                                       NULL,
    "MENU_CHECK_STRICTLY" TINYINT     DEFAULT 1
                                       NULL,
    "DEPT_CHECK_STRICTLY" TINYINT     DEFAULT 1
                                       NULL,
    "STATUS"              CHAR(1)      NOT NULL,
    "DEL_FLAG"            CHAR(1)     DEFAULT '0'
                                       NULL,
    "CREATE_BY"           VARCHAR(64) DEFAULT ''
                                       NULL,
    "CREATE_TIME"         TIMESTAMP(0) NULL,
    "UPDATE_BY"           VARCHAR(64) DEFAULT ''
                                       NULL,
    "UPDATE_TIME"         TIMESTAMP(0) NULL,
    "REMARK"              VARCHAR(500) NULL,
    "ROLE_TYPE"           CHAR(1)     DEFAULT '0'
                                       NOT NULL
);
CREATE TABLE "SYS_ROLE_DEPT"
(
    "ROLE_ID" BIGINT NOT NULL,
    "DEPT_ID" BIGINT NOT NULL
);
CREATE TABLE "SYS_ROLE_MENU"
(
    "ROLE_ID" BIGINT NOT NULL,
    "MENU_ID" BIGINT NOT NULL
);
CREATE TABLE "SYS_USER"
(
    "USER_ID"     BIGINT IDENTITY(188,1) NOT NULL,
    "DEPT_ID"     BIGINT       NULL,
    "USER_NAME"   VARCHAR(30)  NOT NULL,
    "NICK_NAME"   VARCHAR(30)  NOT NULL,
    "USER_TYPE"   VARCHAR(2)   DEFAULT '00'
                               NULL,
    "EMAIL"       VARCHAR(50)  DEFAULT ''
                               NULL,
    "PHONENUMBER" VARCHAR(11)  DEFAULT ''
                               NULL,
    "SEX"         CHAR(1)      DEFAULT '0'
                               NULL,
    "AVATAR"      VARCHAR(100) DEFAULT ''
                               NULL,
    "PASSWORD"    VARCHAR(100) DEFAULT ''
                               NULL,
    "STATUS"      CHAR(1)      DEFAULT '0'
                               NULL,
    "DEL_FLAG"    CHAR(1)      DEFAULT '0'
                               NULL,
    "LOGIN_IP"    VARCHAR(128) DEFAULT ''
                               NULL,
    "LOGIN_DATE"  TIMESTAMP(0) NULL,
    "CREATE_BY"   VARCHAR(64)  DEFAULT ''
                               NULL,
    "CREATE_TIME" TIMESTAMP(0) NULL,
    "UPDATE_BY"   VARCHAR(64)  DEFAULT ''
                               NULL,
    "UPDATE_TIME" TIMESTAMP(0) NULL,
    "REMARK"      VARCHAR(500) NULL
);
CREATE TABLE "SYS_USER_POST"
(
    "USER_ID" BIGINT NOT NULL,
    "POST_ID" BIGINT NOT NULL
);
CREATE TABLE "SYS_USER_ROLE"
(
    "USER_ID" BIGINT NOT NULL,
    "ROLE_ID" BIGINT NOT NULL
);
ALTER TABLE "SYS_ROLE"
    ADD CONSTRAINT PRIMARY KEY ("ROLE_ID");

ALTER TABLE "SYS_ROLE_DEPT"
    ADD CONSTRAINT PRIMARY KEY ("ROLE_ID", "DEPT_ID");

ALTER TABLE "SYS_ROLE_MENU"
    ADD CONSTRAINT PRIMARY KEY ("ROLE_ID", "MENU_ID");

ALTER TABLE "SYS_USER"
    ADD CONSTRAINT PRIMARY KEY ("USER_ID");

ALTER TABLE "SYS_USER_POST"
    ADD CONSTRAINT PRIMARY KEY ("USER_ID", "POST_ID");

ALTER TABLE "SYS_USER_ROLE"
    ADD CONSTRAINT PRIMARY KEY ("USER_ID", "ROLE_ID");

COMMENT ON TABLE "SYS_ROLE" IS '角色信息表';

COMMENT ON COLUMN "SYS_ROLE"."ROLE_ID" IS '角色ID';

COMMENT ON COLUMN "SYS_ROLE"."ROLE_NAME" IS '角色名称';

COMMENT ON COLUMN "SYS_ROLE"."ROLE_KEY" IS '角色权限字符串';

COMMENT ON COLUMN "SYS_ROLE"."ROLE_SORT" IS '显示顺序';

COMMENT ON COLUMN "SYS_ROLE"."DATA_SCOPE" IS '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）';

COMMENT ON COLUMN "SYS_ROLE"."MENU_CHECK_STRICTLY" IS '菜单树选择项是否关联显示';

COMMENT ON COLUMN "SYS_ROLE"."DEPT_CHECK_STRICTLY" IS '部门树选择项是否关联显示';

COMMENT ON COLUMN "SYS_ROLE"."STATUS" IS '角色状态（0正常 1停用）';

COMMENT ON COLUMN "SYS_ROLE"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';

COMMENT ON COLUMN "SYS_ROLE"."CREATE_BY" IS '创建者';

COMMENT ON COLUMN "SYS_ROLE"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "SYS_ROLE"."UPDATE_BY" IS '更新者';

COMMENT ON COLUMN "SYS_ROLE"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "SYS_ROLE"."REMARK" IS '备注';

COMMENT ON COLUMN "SYS_ROLE"."ROLE_TYPE" IS '角色类型：0功能角色；1审批角色';

COMMENT ON TABLE "SYS_ROLE_DEPT" IS '角色和部门关联表';

COMMENT ON COLUMN "SYS_ROLE_DEPT"."ROLE_ID" IS '角色ID';

COMMENT ON COLUMN "SYS_ROLE_DEPT"."DEPT_ID" IS '部门ID';

COMMENT ON TABLE "SYS_ROLE_MENU" IS '角色和菜单关联表';

COMMENT ON COLUMN "SYS_ROLE_MENU"."ROLE_ID" IS '角色ID';

COMMENT ON COLUMN "SYS_ROLE_MENU"."MENU_ID" IS '菜单ID';

COMMENT ON TABLE "SYS_USER" IS '用户信息表';

COMMENT ON COLUMN "SYS_USER"."USER_ID" IS '用户ID';

COMMENT ON COLUMN "SYS_USER"."DEPT_ID" IS '部门ID';

COMMENT ON COLUMN "SYS_USER"."USER_NAME" IS '用户账号';

COMMENT ON COLUMN "SYS_USER"."NICK_NAME" IS '用户昵称';

COMMENT ON COLUMN "SYS_USER"."USER_TYPE" IS '用户类型（00系统用户）';

COMMENT ON COLUMN "SYS_USER"."EMAIL" IS '用户邮箱';

COMMENT ON COLUMN "SYS_USER"."PHONENUMBER" IS '手机号码';

COMMENT ON COLUMN "SYS_USER"."SEX" IS '用户性别（0男 1女 2未知）';

COMMENT ON COLUMN "SYS_USER"."AVATAR" IS '头像地址';

COMMENT ON COLUMN "SYS_USER"."PASSWORD" IS '密码';

COMMENT ON COLUMN "SYS_USER"."STATUS" IS '帐号状态（0正常 1停用）';

COMMENT ON COLUMN "SYS_USER"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';

COMMENT ON COLUMN "SYS_USER"."LOGIN_IP" IS '最后登录IP';

COMMENT ON COLUMN "SYS_USER"."LOGIN_DATE" IS '最后登录时间';

COMMENT ON COLUMN "SYS_USER"."CREATE_BY" IS '创建者';

COMMENT ON COLUMN "SYS_USER"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "SYS_USER"."UPDATE_BY" IS '更新者';

COMMENT ON COLUMN "SYS_USER"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "SYS_USER"."REMARK" IS '备注';

COMMENT ON TABLE "SYS_USER_POST" IS '用户与岗位关联表';

COMMENT ON COLUMN "SYS_USER_POST"."USER_ID" IS '用户ID';

COMMENT ON COLUMN "SYS_USER_POST"."POST_ID" IS '岗位ID';

COMMENT ON TABLE "SYS_USER_ROLE" IS '用户和角色关联表';

COMMENT ON COLUMN "SYS_USER_ROLE"."USER_ID" IS '用户ID';

COMMENT ON COLUMN "SYS_USER_ROLE"."ROLE_ID" IS '角色ID';

CREATE TABLE "CUBE_2D_TABLE"
(
    "ID"             INT IDENTITY(1216,1) NOT NULL,
    "DIRECTORY_ID"   VARCHAR(32)  NULL,
    "TABLE_NAME"     VARCHAR(64)  NULL,
    "CREATE_MODE"    VARCHAR(32)  NULL,
    "VIEW_ID"        VARCHAR(32)  NULL,
    "TABLE_META"     CLOB         NULL,
    "CHILD_FLAG"     VARCHAR(32)  NULL,
    "STATUS"         VARCHAR(32)  NULL,
    "PARENT_ID"      INT          NULL,
    "TYPE"           VARCHAR(32)  NULL,
    "ANCESTORS"      VARCHAR(128) NULL,
    "MEM_TABLE_NAME" VARCHAR(64)  NULL,
    "CREATE_BY"      VARCHAR(64)  NULL,
    "CREATE_TIME"    TIMESTAMP(0) NULL,
    "UPDATE_BY"      VARCHAR(64)  NULL,
    "UPDATE_TIME"    TIMESTAMP(0) NULL,
    "MEMSIZE"        VARCHAR(50)  NULL,
    "TABLE_LEVEL"    INT DEFAULT 0
                                  NULL
);
CREATE TABLE "CUBE_2D_TABLE_GEN"
(
    "ID"              INT IDENTITY(172,1) NOT NULL,
    "TABLE_ID"        INT           NULL,
    "SOURCE_TABLE_ID" INT           NULL,
    "GEN_TYPE"        VARCHAR(32)   NULL,
    "CONFIG"          VARCHAR(8188) NULL
);
CREATE TABLE "CUBE_2D_TABLE_REL"
(
    "ID"              VARCHAR(32) NOT NULL,
    "PARENT_TABLE_ID" INT         NULL,
    "CHILD_TABLE_ID"  INT         NULL,
    "REL_TABLE_NO"    INT         NULL,
    "REL_TABLE_CODE"  VARCHAR(32) NULL,
    "REL_TABLE_NAME"  VARCHAR(64) NULL
);
CREATE TABLE "CUBE_DASHBOARD"
(
    "ID"          VARCHAR(32)  NOT NULL,
    "NAME"        VARCHAR(255) NOT NULL,
    "DEPT_ID"     BIGINT       NOT NULL,
    "PARENT_ID"   VARCHAR(32)  NOT NULL,
    "TYPE"        VARCHAR(30)  NOT NULL,
    "CONFIG"      TEXT         NULL,
    "STATUS"      VARCHAR(32)  NULL,
    "CREATE_BY"   VARCHAR(64)  NULL,
    "CREATE_TIME" TIMESTAMP(0) NULL,
    "UPDATE_BY"   VARCHAR(64)  NULL,
    "UPDATE_TIME" TIMESTAMP(0) NULL
);
CREATE TABLE "CUBE_DIC_ITEM"
(
    "ID"          VARCHAR(32)  NOT NULL,
    "DIC_ID"      VARCHAR(32)  NULL,
    "DIC_CODE"    VARCHAR(64)  NULL,
    "DIC_LABEL"   VARCHAR(64)  NULL,
    "STATUS"      VARCHAR(32)  NULL,
    "CREATE_BY"   VARCHAR(64)  NULL,
    "CREATE_TIME" TIMESTAMP(0) NULL,
    "UPDATE_BY"   VARCHAR(64)  NULL,
    "UPDATE_TIME" TIMESTAMP(0) NULL
);
CREATE TABLE "CUBE_DIM_DIM_REL"
(
    "ID"                   INT IDENTITY(646,1) NOT NULL,
    "TABLE_ID"             INT NULL,
    "RES_DIM_DIRECTORY_ID" INT NULL,
    "RES_INSTANCE_ID"      INT NULL,
    "TAR_DIM_DIRECTORY_ID" INT NULL,
    "TAR_INSTANCE_ID"      INT NULL
);
CREATE TABLE "CUBE_DIM_DIRECTORY"
(
    "ID"                 INT IDENTITY(58,1) NOT NULL,
    "DIM_DIRECTORY_NAME" VARCHAR(128) NULL,
    "DIM_DIRECTORY_TYPE" VARCHAR(32)  NULL,
    "DIM_TYPE"           VARCHAR(32)  NULL,
    "INDEX_NO"           INT          NULL,
    "PARENT_ID"          INT          NULL
);
CREATE TABLE "CUBE_DIM_INSTANCE"
(
    "ID"               INT IDENTITY(263,1) NOT NULL,
    "DIM_DIRECTORY_ID" INT          NULL,
    "DIM_NAME"         VARCHAR(128) NULL,
    "DIM_CODE"         VARCHAR(64)  NULL,
    "INDEX_NO"         INT          NULL,
    "PARENT_ID"        INT          NULL,
    "ANCESTORS"        VARCHAR(512) NULL,
    "IS_LEAF"          VARCHAR(8)   NULL
);
CREATE TABLE "CUBE_DIM_LAYOUT"
(
    "ID"         INT IDENTITY(6,1) NOT NULL,
    "TABLE_ID"   INT          NULL,
    "USER_ID"    VARCHAR(32)  NULL,
    "FILTER_DIM" VARCHAR(255) NULL,
    "ROW_DIM"    VARCHAR(255) NULL,
    "COLUMN_DIM" VARCHAR(255) NULL
);
CREATE TABLE "CUBE_DIM_LAYOUT_FILTER"
(
    "ID"            INT IDENTITY(6,1) NOT NULL,
    "LAYOUT_ID"     INT          NULL,
    "FILTER_DIM"    VARCHAR(255) NULL,
    "FILTER_VALUES" VARCHAR(512) NULL
);
CREATE TABLE "CUBE_DIM_RULE"
(
    "ID"             BIGINT IDENTITY(32,1) NOT NULL,
    "DIM_TABLE_ID"   BIGINT       NULL,
    "RULE_NAME"      VARCHAR(100) NULL,
    "RULE_TYPE"      CHAR(1)      NULL,
    "ORDER_NUM"      INT     DEFAULT 0
                                  NULL,
    "POSITION"       INT     DEFAULT 0
                                  NULL,
    "VERSION_NUM"    INT     DEFAULT 0
                                  NULL,
    "CREATE_BY"      VARCHAR(64)  NULL,
    "CREATE_TIME"    TIMESTAMP(0) NULL,
    "UPDATE_BY"      VARCHAR(64)  NULL,
    "UPDATE_TIME"    TIMESTAMP(0) NULL,
    "DEL_FLAG"       CHAR(1) DEFAULT '0'
                                  NULL,
    "INIT_DIM_VALUE" CHAR(1) DEFAULT '0'
                                  NULL
);
CREATE TABLE "CUBE_DIM_RULE_INDICATOR_OPERATION"
(
    "ID"                       BIGINT IDENTITY(261,1) NOT NULL,
    "DIM_TABLE_ID"             BIGINT       NULL,
    "DIM_RULE_ID"              BIGINT       NULL,
    "DIM_DIRECTORY_ID"         BIGINT       NULL,
    "DIM_DIRECTORY_NAME"       VARCHAR(100) NULL,
    "EFFECT_SCOPE"             TEXT         NULL,
    "EFFECT_SCOPE_SIZE"        INT          NULL,
    "IND_ID"                   BIGINT       NULL,
    "IND_NAME"                 VARCHAR(100) NULL,
    "RULE_EXPRESSION"          TEXT         NULL,
    "INDICATOR_OPERATION_TYPE" CHAR(1)      NULL,
    "CREATE_BY"                VARCHAR(64)  NULL,
    "CREATE_TIME"              TIMESTAMP(0) NULL,
    "UPDATE_BY"                VARCHAR(64)  NULL,
    "UPDATE_TIME"              TIMESTAMP(0) NULL,
    "DEL_FLAG"                 CHAR(1) DEFAULT '0'
                                            NULL,
    "RULE_EXPRESSION_INNER"    TEXT         NULL
);
CREATE TABLE "CUBE_DIM_RULE_INDICATOR_REF"
(
    "ID"               BIGINT IDENTITY(244,1) NOT NULL,
    "DIM_RULE_ID"      BIGINT       NULL,
    "DIM_TABLE_ID"     BIGINT       NULL,
    "IND_ID"           BIGINT       NULL,
    "REF_DIM_TABLE_ID" BIGINT       NULL,
    "REF_IND_ID"       VARCHAR(64)  NULL,
    "CREATE_BY"        VARCHAR(64)  NULL,
    "CREATE_TIME"      TIMESTAMP(0) NULL,
    "UPDATE_BY"        VARCHAR(64)  NULL,
    "UPDATE_TIME"      TIMESTAMP(0) NULL,
    "DEL_FLAG"         CHAR(1) DEFAULT '0'
                                    NULL,
    "REF_TABLE_TYPE"   CHAR(4)      NULL,
    "REF_TYPE"         CHAR(3)      NULL
);
CREATE TABLE "CUBE_DIM_TABLE"
(
    "ID"             INT IDENTITY(121,1) NOT NULL,
    "TABLE_NAME"     VARCHAR(64)  NULL,
    "CREATE_MODE"    VARCHAR(32)  NULL,
    "VIEW_ID"        VARCHAR(32)  NULL,
    "PARENT_ID"      INT          NULL,
    "TYPE"           VARCHAR(32)  NULL,
    "ANCESTORS"      VARCHAR(128) NULL,
    "MEM_TABLE_NAME" VARCHAR(64)  NULL,
    "CREATE_BY"      VARCHAR(64)  NULL,
    "CREATE_TIME"    TIMESTAMP(0) NULL,
    "UPDATE_BY"      VARCHAR(64)  NULL,
    "UPDATE_TIME"    TIMESTAMP(0) NULL
);
CREATE TABLE "CUBE_DIM_TABLE_REL"
(
    "ID"                    INT IDENTITY(1628,1) NOT NULL,
    "TABLE_ID"              INT         NULL,
    "REL_ID"                INT         NULL,
    "REL_TYPE"              VARCHAR(32) NULL,
    "IS_DEFAULT_DIM"        VARCHAR(32) NULL,
    "IS_DEFAULT_COLUMN_DIM" VARCHAR(32) NULL
);
CREATE TABLE "CUBE_GROUP"
(
    "ID"          VARCHAR(32)  NOT NULL,
    "GROUP_CODE"  VARCHAR(64)  NULL,
    "GROUP_NAME"  VARCHAR(64)  NULL,
    "STATUS"      VARCHAR(32)  NULL,
    "CREATE_BY"   VARCHAR(64)  NULL,
    "CREATE_TIME" TIMESTAMP(0) NULL,
    "UPDATE_BY"   VARCHAR(64)  NULL,
    "UPDATE_TIME" TIMESTAMP(0) NULL
);
CREATE TABLE "CUBE_GROUP_INSTANCE"
(
    "ID"                  VARCHAR(32)  NOT NULL,
    "GROUP_ID"            VARCHAR(32)  NULL,
    "GROUP_INSTANCE_NAME" VARCHAR(64)  NULL,
    "GROUP_INSTANCE_DESC" VARCHAR(128) NULL,
    "DIC_FLAG"            VARCHAR(32)  NULL,
    "STORAGE_TYPE"        VARCHAR(32)  NULL,
    "OPERATIONAL_RULE"    VARCHAR(64)  NULL,
    "DECIMAL_PLACES"      INT          NULL,
    "SHOW_FORMAT"         VARCHAR(32)  NULL,
    "PREFIX_CHAR"         VARCHAR(32)  NULL,
    "SUFFIX_CHAR"         VARCHAR(32)  NULL,
    "RAW_SAMPLE"          VARCHAR(255) NULL,
    "SHOW_SAMPLE"         VARCHAR(255) NULL,
    "CONTENT_ALIGN"       VARCHAR(255) NULL,
    "STATUS"              VARCHAR(32)  NULL,
    "CREATE_BY"           VARCHAR(64)  NULL,
    "CREATE_TIME"         TIMESTAMP(0) NULL,
    "UPDATE_BY"           VARCHAR(64)  NULL,
    "UPDATE_TIME"         TIMESTAMP(0) NULL
);
CREATE TABLE "CUBE_IND"
(
    "ID"               INT IDENTITY(497,1) NOT NULL,
    "IND_NAME"         VARCHAR(128) NULL,
    "IND_TYPE"         VARCHAR(32)  NULL,
    "INDEX_NO"         INT          NULL,
    "PARENT_ID"        INT          NULL,
    "LEVEL_NUMBER"     INT          NULL,
    "DATA_FORMAT_ID"   VARCHAR(32)  NULL,
    "FUNCTION_NAME"    VARCHAR(128) NULL,
    "FUNCTION_VALUE"   VARCHAR(128) NULL,
    "AVG_PARMA_IND_ID" VARCHAR(32)  NULL
);
CREATE TABLE "CUBE_JOB"
(
    "JOB_ID"            BIGINT IDENTITY(3,1) NOT NULL,
    "JOB_NAME"          VARCHAR(64)  DEFAULT ''
                                     NOT NULL,
    "JOB_GROUP"         VARCHAR(64)  DEFAULT 'DEFAULT'
                                     NOT NULL,
    "INVOKE_TARGET"     VARCHAR(500) NOT NULL,
    "CRON_EXPRESSION"   VARCHAR(255) DEFAULT ''
                                     NULL,
    "MISFIRE_POLICY"    VARCHAR(20)  DEFAULT '3'
                                     NULL,
    "CONCURRENT"        CHAR(1)      DEFAULT '1'
                                     NULL,
    "STATUS"            CHAR(1)      DEFAULT '0'
                                     NULL,
    "CREATE_BY"         VARCHAR(64)  DEFAULT ''
                                     NULL,
    "CREATE_TIME"       TIMESTAMP(0) NULL,
    "UPDATE_BY"         VARCHAR(64)  DEFAULT ''
                                     NULL,
    "UPDATE_TIME"       TIMESTAMP(0) NULL,
    "REMARK"            VARCHAR(500) DEFAULT ''
                                     NULL,
    "TRIGGER_FREQUENCY" CHAR(1)      DEFAULT '0'
                                     NULL
);
CREATE TABLE "CUBE_JOB_DETAIL"
(
    "ID"                INT IDENTITY(2,1) NOT NULL,
    "JOB_ID"            INT          NULL,
    "JOB_TYPE"          CHAR(1)      NULL,
    "JOB_OPERATE_TYPE"  CHAR(1)      NULL,
    "TABLE_ID"          INT          NULL,
    "TABLE_NAME"        VARCHAR(255) NULL,
    "DATA_VERSION"      VARCHAR(255) NULL,
    "DATA_SOURCE"       VARCHAR(255) NULL,
    "CREATE_BY"         VARCHAR(64) DEFAULT ''
                                     NULL,
    "CREATE_TIME"       TIMESTAMP(0) NULL,
    "UPDATE_BY"         VARCHAR(64) DEFAULT ''
                                     NULL,
    "UPDATE_TIME"       TIMESTAMP(0) NULL,
    "PROCESS_NAME"      VARCHAR(255) NULL,
    "DATA_VERSION_NAME" VARCHAR(255) NULL
);
CREATE TABLE "CUBE_JOB_LOG"
(
    "JOB_LOG_ID"     BIGINT IDENTITY(1151,1) NOT NULL,
    "JOB_NAME"       VARCHAR(64)  NOT NULL,
    "JOB_GROUP"      VARCHAR(64)  NOT NULL,
    "INVOKE_TARGET"  VARCHAR(500) NOT NULL,
    "JOB_MESSAGE"    VARCHAR(500) NULL,
    "STATUS"         CHAR(1)       DEFAULT '0'
                                  NULL,
    "EXCEPTION_INFO" VARCHAR(2000) DEFAULT ''
                                  NULL,
    "JOB_ID"         INT          NULL,
    "START_TIME"     TIMESTAMP(0) NULL,
    "STOP_TIME"      TIMESTAMP(0) NULL
);
CREATE TABLE "CUBE_PERMISSION"
(
    "ID"               VARCHAR(32) NOT NULL,
    "ROLE_ID"          VARCHAR(32) NULL,
    "TABLE_ID"         VARCHAR(32) NULL,
    "DATA_SCOPE"       INT         NULL,
    "TABLE_DEFINITION" INT         NULL,
    "DATA_EDITION"     INT         NULL
);
CREATE TABLE "CUBE_PERMISSION_DIM"
(
    "ID"               VARCHAR(32) NOT NULL,
    "ROLE_ID"          VARCHAR(32) NULL,
    "TABLE_ID"         VARCHAR(32) NULL,
    "DATA_SCOPE"       INT         NULL,
    "TABLE_DEFINITION" INT         NULL,
    "DATA_EDITION"     INT         NULL
);
CREATE TABLE "CUBE_PERMISSION_DIM_INSTANCE"
(
    "ID"               VARCHAR(32) NOT NULL,
    "ROLE_ID"          VARCHAR(32) NULL,
    "TABLE_ID"         VARCHAR(32) NULL,
    "DIM_DIRECTORY_ID" INT         NULL,
    "INSTANCE_ID"      INT         NULL,
    "DIM_TYPE"         VARCHAR(32) NULL,
    "DATA_SCOPE"       INT         NULL
);
CREATE TABLE "CUBE_RULE"
(
    "ID"                 INT IDENTITY(9151,1) NOT NULL,
    "TABLE_ID"           INT          NULL,
    "COLUMN_CODE"        VARCHAR(32)  NULL,
    "RULE_EXPRESS"       TEXT         NULL,
    "RULE_TYPE"          TINYINT      NULL,
    "CREATE_BY"          VARCHAR(64)  NULL,
    "CREATE_TIME"        TIMESTAMP(0) NULL,
    "UPDATE_BY"          VARCHAR(64)  NULL,
    "UPDATE_TIME"        TIMESTAMP(0) NULL,
    "REF_TABLE_ID"       INT          NULL,
    "REF_COLUMN_CODE"    VARCHAR(32)  NULL,
    "RULE_EXPRESS_INNER" TEXT         NULL,
    "DEL_FLAG"           CHAR(1) DEFAULT '0'
                                      NULL
);
CREATE TABLE "CUBE_RULE_FUNC"
(
    "ID"         INT IDENTITY(56,1) NOT NULL,
    "FUNC_NAME"  VARCHAR(255)  NULL,
    "FUNC_VAL"   VARCHAR(255)  NULL,
    "FUNC_DESC"  VARCHAR(8188) NULL,
    "FUNC_GROUP" INT           NULL,
    "FUNC_SEQ"   INT           NULL,
    "ALIAS"      VARCHAR(50)   NULL,
    "FUNC_SCOPE" VARCHAR(10)   NULL
);
CREATE TABLE "CUBE_RULE_FUNC_GROUP"
(
    "ID"         INT          NOT NULL,
    "GROUP_NAME" VARCHAR(255) NULL,
    "GROUP_SEQ"  INT          NULL
);
CREATE TABLE "CUBE_SOURCE"
(
    "ID"               VARCHAR(32)  NOT NULL,
    "SOURCE_NAME"      VARCHAR(64)  NULL,
    "SOURCE_TYPE"      VARCHAR(32)  NULL,
    "SOURCE_CONFIG"    TEXT         NULL,
    "CREATE_BY"        VARCHAR(64)  NULL,
    "CREATE_TIME"      TIMESTAMP(0) NULL,
    "UPDATE_BY"        VARCHAR(64)  NULL,
    "UPDATE_TIME"      TIMESTAMP(0) NULL,
    "DATA_FILE_TYPE"   VARCHAR(100) NULL,
    "DATA_FILE_DIR"    VARCHAR(100) NULL,
    "DIR_DATE_FORMAT"  VARCHAR(20)  NULL,
    "SPLITTER"         VARCHAR(20)  NULL,
    "DATA_FILE_SUFFIX" VARCHAR(50)  NULL,
    "OK_FILE_SUFFIX"   VARCHAR(20)  NULL,
    "FILE_PATH"        VARCHAR(100) NULL
);
CREATE TABLE "CUBE_SOURCE_DETAIL"
(
    "ID"             VARCHAR(32)  NOT NULL,
    "SOURCE_ID"      VARCHAR(32)  NULL,
    "TABLE_NAME"     VARCHAR(64)  NULL,
    "FILE_NAME"      VARCHAR(64)  NULL,
    "TABLE_COMMENTS" VARCHAR(100) NULL,
    "TABLE_META"     TEXT         NULL,
    "CREATE_BY"      VARCHAR(64)  NULL,
    "CREATE_TIME"    TIMESTAMP(0) NULL,
    "UPDATE_BY"      VARCHAR(64)  NULL,
    "UPDATE_TIME"    TIMESTAMP(0) NULL
);
CREATE TABLE "CUBE_TABLE_VERSION"
(
    "ID"                INT IDENTITY(139,1) NOT NULL,
    "TABLE_ID"          INT          NOT NULL,
    "VERSION" VARCHAR(255) NOT NULL,
    "MEM_TABLE_NAME"    VARCHAR(64)  NOT NULL,
    "BACKUP_TABLE_NAME" VARCHAR(64)  NOT NULL,
    "TABLE_META"        TEXT         NOT NULL,
    "CREATE_BY"         VARCHAR(64)  NOT NULL,
    "CREATE_TIME"       TIMESTAMP(0) NOT NULL
);
CREATE TABLE "CUBE_VIEW"
(
    "ID"           VARCHAR(32)  NOT NULL,
    "DIRECTORY_ID" VARCHAR(32)  NULL,
    "SOURCE_ID"    VARCHAR(32)  NULL,
    "VIEW_NAME"    VARCHAR(64)  NULL,
    "VIEW_SCRIPT"  TEXT         NULL,
    "VIEW_META"    TEXT         NULL,
    "STATUS"       VARCHAR(32)  NULL,
    "CREATE_BY"    VARCHAR(64)  NULL,
    "CREATE_TIME"  TIMESTAMP(0) NULL,
    "UPDATE_BY"    VARCHAR(64)  NULL,
    "UPDATE_TIME"  TIMESTAMP(0) NULL
);
CREATE TABLE "CUBE_WF_DEFINE"
(
    "ID"                     BIGINT IDENTITY(12,1) NOT NULL,
    "TABLE_ID"               BIGINT       NULL,
    "FLOW_STATE_COLUMN_NAME" VARCHAR(200) NULL,
    "FLOW_STATE_COLUMN_CODE" VARCHAR(200) NULL,
    "BATCH_SUBMIT"           CHAR(1)      NULL,
    "FLOW_TYPE"              VARCHAR(2)   NULL,
    "FLOW_NAME"              VARCHAR(200) NULL,
    "PARENT_ID"              BIGINT       DEFAULT 0
                                          NULL,
    "TYPE"                   VARCHAR(2)   NULL,
    "ANCESTORS"              VARCHAR(128) DEFAULT '0'
                                          NULL,
    "FLOWCHART_JSON"         TEXT         NULL,
    "CREATE_BY"              VARCHAR(64)  NULL,
    "CREATE_TIME"            TIMESTAMP(0) NULL,
    "UPDATE_BY"              VARCHAR(64)  NULL,
    "UPDATE_TIME"            TIMESTAMP(0) NULL,
    "DEL_FLAG"               CHAR(1)      DEFAULT '0'
                                          NULL
);
CREATE TABLE "CUBE_WF_DIM_DEFINE_CENTRALIZE"
(
    "ID"                   BIGINT IDENTITY(16,1) NOT NULL,
    "DIM_DEFINE_DETAIL_ID" BIGINT       NULL,
    "CENTRALIZE_NAME"      VARCHAR(64)  NULL,
    "CENTRALIZE_DIM_ID"    TEXT         NULL,
    "CENTRALIZE_DIM_SIZE"  INT          NULL,
    "USER_ID"              TEXT         NULL,
    "CREATE_BY"            VARCHAR(64)  NULL,
    "CREATE_TIME"          TIMESTAMP(0) NULL,
    "UPDATE_BY"            VARCHAR(64)  NULL,
    "UPDATE_TIME"          TIMESTAMP(0) NULL,
    "DEL_FLAG"             CHAR(1) DEFAULT '0'
                                        NULL
);
CREATE TABLE "CUBE_WF_DIM_DEFINE_DETAIL"
(
    "ID"                 BIGINT IDENTITY(64,1) NOT NULL,
    "DEFINE_ID"          BIGINT       NULL,
    "DIM_ID"             BIGINT       NULL,
    "USER_ID"            TEXT         NULL,
    "CENTRALIZED_DIM_ID" VARCHAR(200) NULL,
    "CREATE_BY"          VARCHAR(64)  NULL,
    "CREATE_TIME"        TIMESTAMP(0) NULL,
    "UPDATE_BY"          VARCHAR(64)  NULL,
    "UPDATE_TIME"        TIMESTAMP(0) NULL,
    "DEL_FLAG"           CHAR(1) DEFAULT '0'
                                      NULL
);
CREATE TABLE "CUBE_WF_HISTORY"
(
    "ID"                       BIGINT IDENTITY(213,1) NOT NULL,
    "INSTANCE_ID"              BIGINT       NULL,
    "DEFINE_ID"                BIGINT       NULL,
    "TABLE_ID"                 BIGINT       NULL,
    "TASK_ID"                  BIGINT       NULL,
    "DATA_ID"                  TEXT         NULL,
    "COMMENT_CONTENT"          VARCHAR(500) NULL,
    "NODE_ID"                  VARCHAR(100) NULL,
    "NODE_NAME"                VARCHAR(64)  NULL,
    "USER_ID"                  VARCHAR(64)  NULL,
    "USER_NAME"                VARCHAR(128) NULL,
    "CREATE_BY"                VARCHAR(64)  NULL,
    "CREATE_TIME"              TIMESTAMP(0) NULL,
    "UPDATE_BY"                VARCHAR(64)  NULL,
    "UPDATE_TIME"              TIMESTAMP(0) NULL,
    "NEXT_EDGE_ID"             VARCHAR(64)  NULL,
    "NEXT_EDGE_NAME"           VARCHAR(64)  NULL,
    "NEXT_NODE_ID"             VARCHAR(64)  NULL,
    "NEXT_NODE_NAME"           VARCHAR(64)  NULL,
    "DEL_FLAG"                 CHAR(1) DEFAULT '0'
                                            NULL,
    "OPERATE_TYPE"             CHAR(1)      NULL,
    "CENTRALIZE_DIM_DEFINE_ID" BIGINT       NULL
);
CREATE TABLE "CUBE_WF_INSTANCE"
(
    "ID"                 BIGINT IDENTITY(61,1) NOT NULL,
    "DEFINE_ID"          BIGINT       NULL,
    "DEFINE_NAME"        VARCHAR(200) NULL,
    "INSTANCE_STATE"     CHAR(1)      NULL,
    "PROCESS_STARTER"    VARCHAR(128) NULL,
    "PROCESS_STARTER_ID" VARCHAR(64)  NULL,
    "TABLE_ID"           BIGINT       NULL,
    "TABLE_NAME"         VARCHAR(64)  NULL,
    "DATA_ID"            TEXT         NULL,
    "NODE_ID"            VARCHAR(100) NULL,
    "NODE_NAME"          VARCHAR(64)  NULL,
    "CREATE_BY"          VARCHAR(64)  NULL,
    "CREATE_TIME"        TIMESTAMP(0) NULL,
    "UPDATE_BY"          VARCHAR(64)  NULL,
    "UPDATE_TIME"        TIMESTAMP(0) NULL,
    "START_TIME"         TIMESTAMP(0) NULL,
    "END_TIME"           TIMESTAMP(0) NULL,
    "DEL_FLAG"           CHAR(1) DEFAULT '0'
                                      NULL,
    "FLOW_TYPE"          CHAR(1)      NULL
);
CREATE TABLE "CUBE_WF_TASK"
(
    "ID"                       BIGINT IDENTITY(439,1) NOT NULL,
    "INSTANCE_ID"              BIGINT       NULL,
    "DEFINE_ID"                BIGINT       NULL,
    "DEFINE_NAME"              VARCHAR(200) NULL,
    "DATA_ID"                  TEXT         NULL,
    "TASK_DONE"                CHAR(1)      NULL,
    "COST"                     INT          NULL,
    "PROCESS_STARTER"          VARCHAR(128) NULL,
    "PROCESS_STARTER_ID"       VARCHAR(64)  NULL,
    "APPROVE_ROLE_ID"          VARCHAR(200) NULL,
    "APPROVE_ROLE_NAME"        VARCHAR(500) NULL,
    "NEXT_EDGE_ID"             VARCHAR(64)  NULL,
    "NEXT_EDGE_NAME"           VARCHAR(64)  NULL,
    "NEXT_NODE_ID"             VARCHAR(64)  NULL,
    "NEXT_NODE_NAME"           VARCHAR(64)  NULL,
    "TABLE_ID"                 BIGINT       NULL,
    "TABLE_NAME"               VARCHAR(64)  NULL,
    "NODE_ID"                  VARCHAR(100) NULL,
    "NODE_NAME"                VARCHAR(64)  NULL,
    "USER_ID"                  VARCHAR(64)  NULL,
    "USER_NAME"                VARCHAR(128) NULL,
    "COMMENT_CONTENT"          VARCHAR(500) NULL,
    "APPROVE_TIME"             TIMESTAMP(0) NULL,
    "CREATE_BY"                VARCHAR(64)  NULL,
    "CREATE_TIME"              TIMESTAMP(0) NULL,
    "UPDATE_BY"                VARCHAR(64)  NULL,
    "UPDATE_TIME"              TIMESTAMP(0) NULL,
    "DEL_FLAG"                 CHAR(1) DEFAULT '0'
                                            NULL,
    "FLOW_TYPE"                CHAR(1)      NULL,
    "PARENT_NODE_ID"           VARCHAR(100) NULL,
    "CENTRALIZE_DIM_DEFINE_ID" BIGINT       NULL,
    "TASK_STATE"               CHAR(1)      NULL,
    "OPERATE_USER_ID"          VARCHAR(64)  NULL
);
CREATE TABLE "CUBE_WF_VARIABLE"
(
    "ID"            BIGINT IDENTITY(559,1) NOT NULL,
    "INSTANCE_ID"   BIGINT       NULL,
    "NODE_ID"       VARCHAR(100) NULL,
    "CENTRALIZE_ID" VARCHAR(100) NULL,
    "VARIABLE_KEY"  VARCHAR(200) NULL,
    "VARIABLE_VAL"  VARCHAR(200) NULL,
    "CREATE_BY"     VARCHAR(64)  NULL,
    "CREATE_TIME"   TIMESTAMP(0) NULL,
    "UPDATE_BY"     VARCHAR(64)  NULL,
    "UPDATE_TIME"   TIMESTAMP(0) NULL,
    "DEL_FLAG"      CHAR(1) DEFAULT '0'
                                 NULL
);
CREATE TABLE "CUBE_WIDGET"
(
    "ID"           VARCHAR(32)  NOT NULL,
    "NAME"         VARCHAR(255) NULL,
    "VIEW_ID"      VARCHAR(32)  NULL,
    "VIEW_NAME"    VARCHAR(64)  NULL,
    "DASHBOARD_ID" VARCHAR(32)  NOT NULL,
    "CONFIG"       TEXT         NULL,
    "SEQ"          INT          NULL,
    "CREATE_BY"    VARCHAR(64)  NULL,
    "CREATE_TIME"  TIMESTAMP(0) NULL,
    "UPDATE_BY"    VARCHAR(64)  NULL,
    "UPDATE_TIME"  TIMESTAMP(0) NULL
);
CREATE TABLE "GEN_TABLE"
(
    "TABLE_ID"          BIGINT IDENTITY(1,1) NOT NULL,
    "TABLE_NAME"        VARCHAR(200) DEFAULT ''
                                      NULL,
    "TABLE_COMMENT"     VARCHAR(500) DEFAULT ''
                                      NULL,
    "SUB_TABLE_NAME"    VARCHAR(64)   NULL,
    "SUB_TABLE_FK_NAME" VARCHAR(64)   NULL,
    "CLASS_NAME"        VARCHAR(100) DEFAULT ''
                                      NULL,
    "TPL_CATEGORY"      VARCHAR(200) DEFAULT 'crud'
                                      NULL,
    "PACKAGE_NAME"      VARCHAR(100)  NULL,
    "MODULE_NAME"       VARCHAR(30)   NULL,
    "BUSINESS_NAME"     VARCHAR(30)   NULL,
    "FUNCTION_NAME"     VARCHAR(50)   NULL,
    "FUNCTION_AUTHOR"   VARCHAR(50)   NULL,
    "GEN_TYPE"          CHAR(1)      DEFAULT '0'
                                      NULL,
    "GEN_PATH"          VARCHAR(200) DEFAULT '/'
                                      NULL,
    "OPTIONS"           VARCHAR(1000) NULL,
    "CREATE_BY"         VARCHAR(64)  DEFAULT ''
                                      NULL,
    "CREATE_TIME"       TIMESTAMP(0)  NULL,
    "UPDATE_BY"         VARCHAR(64)  DEFAULT ''
                                      NULL,
    "UPDATE_TIME"       TIMESTAMP(0)  NULL,
    "REMARK"            VARCHAR(500)  NULL
);
CREATE TABLE "GEN_TABLE_COLUMN"
(
    "COLUMN_ID"      BIGINT IDENTITY(1,1) NOT NULL,
    "TABLE_ID"       VARCHAR(64)  NULL,
    "COLUMN_NAME"    VARCHAR(200) NULL,
    "COLUMN_COMMENT" VARCHAR(500) NULL,
    "COLUMN_TYPE"    VARCHAR(100) NULL,
    "JAVA_TYPE"      VARCHAR(500) NULL,
    "JAVA_FIELD"     VARCHAR(200) NULL,
    "IS_PK"          CHAR(1)      NULL,
    "IS_INCREMENT"   CHAR(1)      NULL,
    "IS_REQUIRED"    CHAR(1)      NULL,
    "IS_INSERT"      CHAR(1)      NULL,
    "IS_EDIT"        CHAR(1)      NULL,
    "IS_LIST"        CHAR(1)      NULL,
    "IS_QUERY"       CHAR(1)      NULL,
    "QUERY_TYPE"     VARCHAR(200) DEFAULT 'EQ'
                                  NULL,
    "HTML_TYPE"      VARCHAR(200) NULL,
    "DICT_TYPE"      VARCHAR(200) DEFAULT ''
                                  NULL,
    "SORT"           INT          NULL,
    "CREATE_BY"      VARCHAR(64)  DEFAULT ''
                                  NULL,
    "CREATE_TIME"    TIMESTAMP(0) NULL,
    "UPDATE_BY"      VARCHAR(64)  DEFAULT ''
                                  NULL,
    "UPDATE_TIME"    TIMESTAMP(0) NULL
);
CREATE TABLE "SYS_CONFIG"
(
    "CONFIG_ID"    INT IDENTITY(8,1) NOT NULL,
    "CONFIG_NAME"  VARCHAR(100) DEFAULT ''
                                NULL,
    "CONFIG_KEY"   VARCHAR(100) DEFAULT ''
                                NULL,
    "CONFIG_VALUE" VARCHAR(500) DEFAULT ''
                                NULL,
    "CONFIG_TYPE"  CHAR(1)      DEFAULT 'N'
                                NULL,
    "CREATE_BY"    VARCHAR(64)  DEFAULT ''
                                NULL,
    "CREATE_TIME"  TIMESTAMP(0) NULL,
    "UPDATE_BY"    VARCHAR(64)  DEFAULT ''
                                NULL,
    "UPDATE_TIME"  TIMESTAMP(0) NULL,
    "REMARK"       VARCHAR(500) NULL
);
CREATE TABLE "SYS_DEPT"
(
    "DEPT_ID"     BIGINT IDENTITY(1785119491030536196,1) NOT NULL,
    "PARENT_ID"   BIGINT      DEFAULT 0
                               NULL,
    "ANCESTORS"   VARCHAR(50) DEFAULT ''
                               NULL,
    "DEPT_NAME"   VARCHAR(30) DEFAULT ''
                               NULL,
    "ORDER_NUM"   INT         DEFAULT 0
                               NULL,
    "LEADER"      VARCHAR(20)  NULL,
    "PHONE"       VARCHAR(11)  NULL,
    "EMAIL"       VARCHAR(50)  NULL,
    "STATUS"      CHAR(1)     DEFAULT '0'
                               NULL,
    "DEL_FLAG"    CHAR(1)     DEFAULT '0'
                               NULL,
    "CREATE_BY"   VARCHAR(64) DEFAULT ''
                               NULL,
    "CREATE_TIME" TIMESTAMP(0) NULL,
    "UPDATE_BY"   VARCHAR(64) DEFAULT ''
                               NULL,
    "UPDATE_TIME" TIMESTAMP(0) NULL,
    "DEPT_TYPE"   VARCHAR(100) NULL,
    "DEPT_LEVEL"  VARCHAR(100) NULL
);
CREATE TABLE "SYS_DICT_DATA"
(
    "DICT_CODE"   BIGINT IDENTITY(108,1) NOT NULL,
    "DICT_SORT"   INT          DEFAULT 0
                               NULL,
    "DICT_LABEL"  VARCHAR(100) DEFAULT ''
                               NULL,
    "DICT_VALUE"  VARCHAR(100) DEFAULT ''
                               NULL,
    "DICT_TYPE"   VARCHAR(100) DEFAULT ''
                               NULL,
    "CSS_CLASS"   VARCHAR(100) NULL,
    "LIST_CLASS"  VARCHAR(100) NULL,
    "IS_DEFAULT"  CHAR(1)      DEFAULT 'N'
                               NULL,
    "STATUS"      CHAR(1)      DEFAULT '0'
                               NULL,
    "CREATE_BY"   VARCHAR(64)  DEFAULT ''
                               NULL,
    "CREATE_TIME" TIMESTAMP(0) NULL,
    "UPDATE_BY"   VARCHAR(64)  DEFAULT ''
                               NULL,
    "UPDATE_TIME" TIMESTAMP(0) NULL,
    "REMARK"      VARCHAR(500) NULL
);
CREATE TABLE "SYS_DICT_TYPE"
(
    "DICT_ID"     BIGINT IDENTITY(14,1) NOT NULL,
    "DICT_NAME"   VARCHAR(100) DEFAULT ''
                               NULL,
    "DICT_TYPE"   VARCHAR(100) DEFAULT ''
                               NULL,
    "STATUS"      CHAR(1)      DEFAULT '0'
                               NULL,
    "CREATE_BY"   VARCHAR(64)  DEFAULT ''
                               NULL,
    "CREATE_TIME" TIMESTAMP(0) NULL,
    "UPDATE_BY"   VARCHAR(64)  DEFAULT ''
                               NULL,
    "UPDATE_TIME" TIMESTAMP(0) NULL,
    "REMARK"      VARCHAR(500) NULL
);
CREATE TABLE "SYS_LOGININFOR"
(
    "INFO_ID"        BIGINT IDENTITY(5976,1) NOT NULL,
    "USER_NAME"      VARCHAR(50)  DEFAULT ''
                                  NULL,
    "IPADDR"         VARCHAR(128) DEFAULT ''
                                  NULL,
    "LOGIN_LOCATION" VARCHAR(255) DEFAULT ''
                                  NULL,
    "BROWSER"        VARCHAR(50)  DEFAULT ''
                                  NULL,
    "OS"             VARCHAR(50)  DEFAULT ''
                                  NULL,
    "STATUS"         CHAR(1)      DEFAULT '0'
                                  NULL,
    "MSG"            VARCHAR(255) DEFAULT ''
                                  NULL,
    "LOGIN_TIME"     TIMESTAMP(0) NULL
);
CREATE TABLE "SYS_MENU"
(
    "MENU_ID"     BIGINT IDENTITY(2010,1) NOT NULL,
    "MENU_NAME"   VARCHAR(50)  NOT NULL,
    "PARENT_ID"   BIGINT       DEFAULT 0
                               NULL,
    "ORDER_NUM"   INT          DEFAULT 0
                               NULL,
    "PATH"        VARCHAR(200) DEFAULT ''
                               NULL,
    "COMPONENT"   VARCHAR(255) NULL,
    "QUERY"       VARCHAR(255) NULL,
    "IS_FRAME"    INT          DEFAULT 1
                               NULL,
    "IS_CACHE"    INT          DEFAULT 0
                               NULL,
    "MENU_TYPE"   CHAR(1)      DEFAULT ''
                               NULL,
    "VISIBLE"     CHAR(1)      DEFAULT '0'
                               NULL,
    "STATUS"      CHAR(1)      DEFAULT '0'
                               NULL,
    "PERMS"       VARCHAR(100) NULL,
    "ICON"        VARCHAR(100) DEFAULT '#'
                               NULL,
    "CREATE_BY"   VARCHAR(64)  DEFAULT ''
                               NULL,
    "CREATE_TIME" TIMESTAMP(0) NULL,
    "UPDATE_BY"   VARCHAR(64)  DEFAULT ''
                               NULL,
    "UPDATE_TIME" TIMESTAMP(0) NULL,
    "REMARK"      VARCHAR(500) DEFAULT ''
                               NULL
);
CREATE TABLE "SYS_NOTICE"
(
    "NOTICE_ID"      INT IDENTITY(4,1) NOT NULL,
    "NOTICE_TITLE"   VARCHAR(50)  NOT NULL,
    "NOTICE_TYPE"    CHAR(1)      NOT NULL,
    "NOTICE_CONTENT" BLOB         NULL,
    "STATUS"         CHAR(1)     DEFAULT '0'
                                  NULL,
    "CREATE_BY"      VARCHAR(64) DEFAULT ''
                                  NULL,
    "CREATE_TIME"    TIMESTAMP(0) NULL,
    "UPDATE_BY"      VARCHAR(64) DEFAULT ''
                                  NULL,
    "UPDATE_TIME"    TIMESTAMP(0) NULL,
    "REMARK"         VARCHAR(255) NULL
);
CREATE TABLE "SYS_OPER_LOG"
(
    "OPER_ID"        BIGINT IDENTITY(18065,1) NOT NULL,
    "TITLE"          VARCHAR(50)   DEFAULT ''
                                  NULL,
    "BUSINESS_TYPE"  INT           DEFAULT 0
                                  NULL,
    "METHOD"         VARCHAR(100)  DEFAULT ''
                                  NULL,
    "REQUEST_METHOD" VARCHAR(10)   DEFAULT ''
                                  NULL,
    "OPERATOR_TYPE"  INT           DEFAULT 0
                                  NULL,
    "OPER_NAME"      VARCHAR(50)   DEFAULT ''
                                  NULL,
    "DEPT_NAME"      VARCHAR(50)   DEFAULT ''
                                  NULL,
    "OPER_URL"       VARCHAR(255)  DEFAULT ''
                                  NULL,
    "OPER_IP"        VARCHAR(128)  DEFAULT ''
                                  NULL,
    "OPER_LOCATION"  VARCHAR(255)  DEFAULT ''
                                  NULL,
    "OPER_PARAM"     VARCHAR(8188) DEFAULT ''
                                  NULL,
    "JSON_RESULT"    VARCHAR(8188) DEFAULT ''
                                  NULL,
    "STATUS"         INT           DEFAULT 0
                                  NULL,
    "ERROR_MSG"      VARCHAR(8188) DEFAULT ''
                                  NULL,
    "OPER_TIME"      TIMESTAMP(0) NULL
);
CREATE TABLE "SYS_POST"
(
    "POST_ID"     BIGINT IDENTITY(5,1) NOT NULL,
    "POST_CODE"   VARCHAR(64)  NOT NULL,
    "POST_NAME"   VARCHAR(50)  NOT NULL,
    "POST_SORT"   INT          NOT NULL,
    "STATUS"      CHAR(1)      NOT NULL,
    "CREATE_BY"   VARCHAR(64) DEFAULT ''
                               NULL,
    "CREATE_TIME" TIMESTAMP(0) NULL,
    "UPDATE_BY"   VARCHAR(64) DEFAULT ''
                               NULL,
    "UPDATE_TIME" TIMESTAMP(0) NULL,
    "REMARK"      VARCHAR(500) NULL
);
ALTER TABLE "CUBE_2D_TABLE"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_2D_TABLE_GEN"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_2D_TABLE_REL"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_DASHBOARD"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_DIM_DIM_REL"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_DIM_DIRECTORY"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_DIM_INSTANCE"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_DIM_LAYOUT"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_DIM_LAYOUT_FILTER"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_DIM_RULE"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_DIM_RULE_INDICATOR_OPERATION"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_DIM_RULE_INDICATOR_REF"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_DIM_TABLE"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_DIM_TABLE_REL"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_IND"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_JOB"
    ADD CONSTRAINT PRIMARY KEY ("JOB_ID", "JOB_NAME", "JOB_GROUP");

ALTER TABLE "CUBE_JOB_DETAIL"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_JOB_LOG"
    ADD CONSTRAINT PRIMARY KEY ("JOB_LOG_ID");

ALTER TABLE "CUBE_PERMISSION"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_PERMISSION_DIM"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_PERMISSION_DIM_INSTANCE"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_RULE"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_RULE_FUNC"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_RULE_FUNC_GROUP"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_TABLE_VERSION"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_WF_DEFINE"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_WF_DIM_DEFINE_CENTRALIZE"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_WF_DIM_DEFINE_DETAIL"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_WF_HISTORY"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_WF_INSTANCE"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_WF_TASK"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_WF_VARIABLE"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "CUBE_WIDGET"
    ADD CONSTRAINT PRIMARY KEY ("ID");

ALTER TABLE "GEN_TABLE"
    ADD CONSTRAINT PRIMARY KEY ("TABLE_ID");

ALTER TABLE "GEN_TABLE_COLUMN"
    ADD CONSTRAINT PRIMARY KEY ("COLUMN_ID");

ALTER TABLE "SYS_CONFIG"
    ADD CONSTRAINT PRIMARY KEY ("CONFIG_ID");

ALTER TABLE "SYS_DEPT"
    ADD CONSTRAINT PRIMARY KEY ("DEPT_ID");

ALTER TABLE "SYS_DICT_DATA"
    ADD CONSTRAINT PRIMARY KEY ("DICT_CODE");

ALTER TABLE "SYS_DICT_TYPE"
    ADD CONSTRAINT PRIMARY KEY ("DICT_ID");

ALTER TABLE "SYS_LOGININFOR"
    ADD CONSTRAINT PRIMARY KEY ("INFO_ID");

ALTER TABLE "SYS_MENU"
    ADD CONSTRAINT PRIMARY KEY ("MENU_ID");

ALTER TABLE "SYS_NOTICE"
    ADD CONSTRAINT PRIMARY KEY ("NOTICE_ID");

ALTER TABLE "SYS_OPER_LOG"
    ADD CONSTRAINT PRIMARY KEY ("OPER_ID");

ALTER TABLE "SYS_POST"
    ADD CONSTRAINT PRIMARY KEY ("POST_ID");

CREATE INDEX "CUBE_2D_TABLE_PARENT_ID_INDEX"
    ON "CUBE_2D_TABLE" ("PARENT_ID");

CREATE INDEX "CUBE_2D_TABLE_REL_CHILD_TABLE_ID_INDEX"
    ON "CUBE_2D_TABLE_REL" ("CHILD_TABLE_ID");

CREATE INDEX "CUBE_2D_TABLE_REL_PARENT_TABLE_ID_INDEX"
    ON "CUBE_2D_TABLE_REL" ("PARENT_TABLE_ID");

CREATE INDEX "DEPT_ID"
    ON "CUBE_DASHBOARD" ("DEPT_ID");

CREATE INDEX "CUBE_DIM_DIM_REL_TABLE_ID_RES_INSTANCE_ID_INDEX"
    ON "CUBE_DIM_DIM_REL" ("TABLE_ID", "RES_INSTANCE_ID");

CREATE INDEX "CUBE_DIM_INSTANCE_DIM_DIRECTORY_ID_ANCESTORS_INDEX"
    ON "CUBE_DIM_INSTANCE" ("DIM_DIRECTORY_ID", "ANCESTORS");

CREATE INDEX "CUBE_DIM_RULE_DIM_TABLE_ID_INDEX"
    ON "CUBE_DIM_RULE" ("DIM_TABLE_ID");

CREATE INDEX "CUBE_DIM_RULE_INDICATOR_OPERATION_IND_ID_OPERATION_TYPE_INDEX"
    ON "CUBE_DIM_RULE_INDICATOR_OPERATION" ("IND_ID", "INDICATOR_OPERATION_TYPE");

CREATE INDEX "CUBE_DIM_RULE_INDICATOR_OPERATION_TABLE_ID_OPERATION_TYPE_INDEX"
    ON "CUBE_DIM_RULE_INDICATOR_OPERATION" ("DIM_TABLE_ID", "INDICATOR_OPERATION_TYPE");

CREATE INDEX "CUBE_DIM_TABLE_REL_TABLE_ID_INDEX"
    ON "CUBE_DIM_TABLE_REL" ("TABLE_ID");

CREATE INDEX "CUBE_GROUP_INSTANCE_GROUP_INSTANCE_NAME_INDEX"
    ON "CUBE_GROUP_INSTANCE" ("GROUP_INSTANCE_NAME");

CREATE INDEX "I_JOB_DETAIL_JOB_ID"
    ON "CUBE_JOB_DETAIL" ("JOB_ID");

CREATE INDEX "I_JOB_LOG_JOB_ID"
    ON "CUBE_JOB_LOG" ("JOB_ID");

CREATE INDEX "CUBE_RULE_FUNC_FUNC_GROUP_INDEX"
    ON "CUBE_RULE_FUNC" ("FUNC_GROUP");

CREATE INDEX "CUBE_WF_DIM_DEFINE_CENTRALIZE_DIM_DEFINE_DETAIL_ID_INDEX"
    ON "CUBE_WF_DIM_DEFINE_CENTRALIZE" ("DIM_DEFINE_DETAIL_ID", "DEL_FLAG");

CREATE INDEX "CUBE_WF_DIM_DEFINE_DETAIL_DEFINE_ID_INDEX"
    ON "CUBE_WF_DIM_DEFINE_DETAIL" ("DEFINE_ID", "DEL_FLAG");

CREATE INDEX "CUBE_WF_TASK_INSTANCE_ID_INDEX"
    ON "CUBE_WF_TASK" ("INSTANCE_ID", "DEL_FLAG");

ALTER TABLE "SYS_DICT_TYPE"
    ADD CONSTRAINT "DICT_TYPE" UNIQUE ("DICT_TYPE");

COMMENT ON TABLE "CUBE_2D_TABLE" IS '二维数据表';

COMMENT ON COLUMN "CUBE_2D_TABLE"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_2D_TABLE"."DIRECTORY_ID" IS '目录ID';

COMMENT ON COLUMN "CUBE_2D_TABLE"."TABLE_NAME" IS '表格名称';

COMMENT ON COLUMN "CUBE_2D_TABLE"."CREATE_MODE" IS '建表方式';

COMMENT ON COLUMN "CUBE_2D_TABLE"."VIEW_ID" IS '数据视图ID';

COMMENT ON COLUMN "CUBE_2D_TABLE"."CHILD_FLAG" IS '子表标记';

COMMENT ON COLUMN "CUBE_2D_TABLE"."STATUS" IS '状态';

COMMENT ON COLUMN "CUBE_2D_TABLE"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_2D_TABLE"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_2D_TABLE"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_2D_TABLE"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CUBE_2D_TABLE"."MEMSIZE" IS '占用内存';

COMMENT ON COLUMN "CUBE_2D_TABLE"."TABLE_LEVEL" IS '表层级';

COMMENT ON TABLE "CUBE_2D_TABLE_GEN" IS '二维表数据生成配置表';

COMMENT ON COLUMN "CUBE_2D_TABLE_GEN"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_2D_TABLE_GEN"."TABLE_ID" IS '维度表ID';

COMMENT ON COLUMN "CUBE_2D_TABLE_GEN"."SOURCE_TABLE_ID" IS '源维度表ID';

COMMENT ON COLUMN "CUBE_2D_TABLE_GEN"."GEN_TYPE" IS '生成规则类型';

COMMENT ON COLUMN "CUBE_2D_TABLE_GEN"."CONFIG" IS '规则配置';

COMMENT ON TABLE "CUBE_2D_TABLE_REL" IS '二维父子关联表';

COMMENT ON COLUMN "CUBE_2D_TABLE_REL"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_2D_TABLE_REL"."PARENT_TABLE_ID" IS '父表ID';

COMMENT ON COLUMN "CUBE_2D_TABLE_REL"."CHILD_TABLE_ID" IS '子表ID';

COMMENT ON COLUMN "CUBE_2D_TABLE_REL"."REL_TABLE_NO" IS '关联父表列序号';

COMMENT ON COLUMN "CUBE_2D_TABLE_REL"."REL_TABLE_CODE" IS '关联父表列编码';

COMMENT ON COLUMN "CUBE_2D_TABLE_REL"."REL_TABLE_NAME" IS '关联父表列名称';

COMMENT ON COLUMN "CUBE_DASHBOARD"."ID" IS '主键';

COMMENT ON COLUMN "CUBE_DASHBOARD"."NAME" IS '名称';

COMMENT ON COLUMN "CUBE_DASHBOARD"."DEPT_ID" IS '组织id';

COMMENT ON COLUMN "CUBE_DASHBOARD"."PARENT_ID" IS '父Id';

COMMENT ON COLUMN "CUBE_DASHBOARD"."TYPE" IS '类型';

COMMENT ON COLUMN "CUBE_DASHBOARD"."CONFIG" IS '配置';

COMMENT ON COLUMN "CUBE_DASHBOARD"."STATUS" IS '状态';

COMMENT ON COLUMN "CUBE_DASHBOARD"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_DASHBOARD"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_DASHBOARD"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_DASHBOARD"."UPDATE_TIME" IS '更新时间';

COMMENT ON TABLE "CUBE_DIC_ITEM" IS '字典值表';

COMMENT ON COLUMN "CUBE_DIC_ITEM"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_DIC_ITEM"."DIC_ID" IS '字典ID';

COMMENT ON COLUMN "CUBE_DIC_ITEM"."DIC_CODE" IS '存储编码';

COMMENT ON COLUMN "CUBE_DIC_ITEM"."DIC_LABEL" IS '显示文字';

COMMENT ON COLUMN "CUBE_DIC_ITEM"."STATUS" IS '状态';

COMMENT ON COLUMN "CUBE_DIC_ITEM"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_DIC_ITEM"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_DIC_ITEM"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_DIC_ITEM"."UPDATE_TIME" IS '更新时间';

COMMENT ON TABLE "CUBE_DIM_DIM_REL" IS '维度关联表';

COMMENT ON COLUMN "CUBE_DIM_DIM_REL"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_DIM_DIM_REL"."TABLE_ID" IS '维度表ID';

COMMENT ON COLUMN "CUBE_DIM_DIM_REL"."RES_DIM_DIRECTORY_ID" IS '源维度目录ID';

COMMENT ON COLUMN "CUBE_DIM_DIM_REL"."RES_INSTANCE_ID" IS '源维度成员ID';

COMMENT ON COLUMN "CUBE_DIM_DIM_REL"."TAR_DIM_DIRECTORY_ID" IS '目标维度目录ID';

COMMENT ON COLUMN "CUBE_DIM_DIM_REL"."TAR_INSTANCE_ID" IS '目标维度成员ID';

COMMENT ON TABLE "CUBE_DIM_DIRECTORY" IS '维度目录表';

COMMENT ON COLUMN "CUBE_DIM_DIRECTORY"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_DIM_DIRECTORY"."DIM_DIRECTORY_NAME" IS '维度目录名称';

COMMENT ON COLUMN "CUBE_DIM_DIRECTORY"."DIM_DIRECTORY_TYPE" IS '维度目录类型';

COMMENT ON COLUMN "CUBE_DIM_DIRECTORY"."DIM_TYPE" IS '维度类型';

COMMENT ON COLUMN "CUBE_DIM_DIRECTORY"."INDEX_NO" IS '显示序号';

COMMENT ON COLUMN "CUBE_DIM_DIRECTORY"."PARENT_ID" IS '上级目录ID';

COMMENT ON TABLE "CUBE_DIM_INSTANCE" IS '维度成员表';

COMMENT ON COLUMN "CUBE_DIM_INSTANCE"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_DIM_INSTANCE"."DIM_DIRECTORY_ID" IS '维度目录ID';

COMMENT ON COLUMN "CUBE_DIM_INSTANCE"."DIM_NAME" IS '维度成员名称';

COMMENT ON COLUMN "CUBE_DIM_INSTANCE"."DIM_CODE" IS '维度成员代码';

COMMENT ON COLUMN "CUBE_DIM_INSTANCE"."INDEX_NO" IS '显示序号';

COMMENT ON COLUMN "CUBE_DIM_INSTANCE"."PARENT_ID" IS '上级维度成员ID';

COMMENT ON COLUMN "CUBE_DIM_INSTANCE"."ANCESTORS" IS '节点路径';

COMMENT ON COLUMN "CUBE_DIM_INSTANCE"."IS_LEAF" IS '是否叶子';

COMMENT ON TABLE "CUBE_DIM_LAYOUT" IS '多维表布局表';

COMMENT ON COLUMN "CUBE_DIM_LAYOUT"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_DIM_LAYOUT"."TABLE_ID" IS '维度表ID';

COMMENT ON COLUMN "CUBE_DIM_LAYOUT"."USER_ID" IS '用户ID';

COMMENT ON COLUMN "CUBE_DIM_LAYOUT"."FILTER_DIM" IS '筛选维度列表';

COMMENT ON COLUMN "CUBE_DIM_LAYOUT"."ROW_DIM" IS '行维度列表';

COMMENT ON COLUMN "CUBE_DIM_LAYOUT"."COLUMN_DIM" IS '列维度列表';

COMMENT ON TABLE "CUBE_DIM_LAYOUT_FILTER" IS '多维表布局筛选维表';

COMMENT ON COLUMN "CUBE_DIM_LAYOUT_FILTER"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_DIM_LAYOUT_FILTER"."LAYOUT_ID" IS '维度布局表ID';

COMMENT ON COLUMN "CUBE_DIM_LAYOUT_FILTER"."FILTER_DIM" IS '筛选维度';

COMMENT ON COLUMN "CUBE_DIM_LAYOUT_FILTER"."FILTER_VALUES" IS '选中维值';

COMMENT ON TABLE "CUBE_DIM_RULE" IS '多维计算规则';

COMMENT ON COLUMN "CUBE_DIM_RULE"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_DIM_RULE"."DIM_TABLE_ID" IS '多维表ID';

COMMENT ON COLUMN "CUBE_DIM_RULE"."RULE_NAME" IS '规则名称';

COMMENT ON COLUMN "CUBE_DIM_RULE"."RULE_TYPE" IS '规则类型（1指标运算）';

COMMENT ON COLUMN "CUBE_DIM_RULE"."ORDER_NUM" IS '规则序号';

COMMENT ON COLUMN "CUBE_DIM_RULE"."POSITION" IS '规则顺序';

COMMENT ON COLUMN "CUBE_DIM_RULE"."VERSION_NUM" IS '版本号';

COMMENT ON COLUMN "CUBE_DIM_RULE"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_DIM_RULE"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_DIM_RULE"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_DIM_RULE"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CUBE_DIM_RULE"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';

COMMENT ON COLUMN "CUBE_DIM_RULE"."INIT_DIM_VALUE" IS '初始化维度';

COMMENT ON TABLE "CUBE_DIM_RULE_INDICATOR_OPERATION" IS '多维计算规则指标运算';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_OPERATION"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_OPERATION"."DIM_TABLE_ID" IS '多维表ID';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_OPERATION"."DIM_RULE_ID" IS '多维表规则ID';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_OPERATION"."DIM_DIRECTORY_ID" IS '维度ID';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_OPERATION"."DIM_DIRECTORY_NAME" IS '维度名称';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_OPERATION"."EFFECT_SCOPE" IS '生效范围';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_OPERATION"."EFFECT_SCOPE_SIZE" IS '生效范围数量';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_OPERATION"."IND_ID" IS '指标ID';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_OPERATION"."IND_NAME" IS '指标名称';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_OPERATION"."RULE_EXPRESSION" IS '规则表达式';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_OPERATION"."INDICATOR_OPERATION_TYPE" IS '指标运算类型（1作用范围，2规则）';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_OPERATION"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_OPERATION"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_OPERATION"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_OPERATION"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_OPERATION"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_OPERATION"."RULE_EXPRESSION_INNER" IS '规则表达式（内部）';

COMMENT ON TABLE "CUBE_DIM_RULE_INDICATOR_REF" IS '多维计算规则引用关系';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_REF"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_REF"."DIM_RULE_ID" IS '规则id';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_REF"."DIM_TABLE_ID" IS '表id';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_REF"."IND_ID" IS '列编码';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_REF"."REF_DIM_TABLE_ID" IS '引用表id';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_REF"."REF_IND_ID" IS '引用列编码';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_REF"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_REF"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_REF"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_REF"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_REF"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_REF"."REF_TABLE_TYPE" IS '引用表类型，二维｜多维';

COMMENT ON COLUMN "CUBE_DIM_RULE_INDICATOR_REF"."REF_TYPE" IS '引用类型，指标｜维度';

COMMENT ON TABLE "CUBE_DIM_TABLE" IS '多维数据表';

COMMENT ON COLUMN "CUBE_DIM_TABLE"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_DIM_TABLE"."TABLE_NAME" IS '表格名称';

COMMENT ON COLUMN "CUBE_DIM_TABLE"."CREATE_MODE" IS '建表方式';

COMMENT ON COLUMN "CUBE_DIM_TABLE"."VIEW_ID" IS '数据视图ID';

COMMENT ON COLUMN "CUBE_DIM_TABLE"."PARENT_ID" IS '父ID';

COMMENT ON COLUMN "CUBE_DIM_TABLE"."TYPE" IS '类型';

COMMENT ON COLUMN "CUBE_DIM_TABLE"."ANCESTORS" IS '路径';

COMMENT ON COLUMN "CUBE_DIM_TABLE"."MEM_TABLE_NAME" IS '内存表名';

COMMENT ON COLUMN "CUBE_DIM_TABLE"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_DIM_TABLE"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_DIM_TABLE"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_DIM_TABLE"."UPDATE_TIME" IS '更新时间';

COMMENT ON TABLE "CUBE_DIM_TABLE_REL" IS '多维表关联表';

COMMENT ON COLUMN "CUBE_DIM_TABLE_REL"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_DIM_TABLE_REL"."TABLE_ID" IS '维度表ID';

COMMENT ON COLUMN "CUBE_DIM_TABLE_REL"."REL_ID" IS '关联ID';

COMMENT ON COLUMN "CUBE_DIM_TABLE_REL"."REL_TYPE" IS '关联类型';

COMMENT ON COLUMN "CUBE_DIM_TABLE_REL"."IS_DEFAULT_DIM" IS '默认行维';

COMMENT ON COLUMN "CUBE_DIM_TABLE_REL"."IS_DEFAULT_COLUMN_DIM" IS '默认列维';

COMMENT ON TABLE "CUBE_GROUP" IS '分组表';

COMMENT ON COLUMN "CUBE_GROUP"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_GROUP"."GROUP_CODE" IS '分组代码';

COMMENT ON COLUMN "CUBE_GROUP"."GROUP_NAME" IS '分组名称';

COMMENT ON COLUMN "CUBE_GROUP"."STATUS" IS '状态';

COMMENT ON COLUMN "CUBE_GROUP"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_GROUP"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_GROUP"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_GROUP"."UPDATE_TIME" IS '更新时间';

COMMENT ON TABLE "CUBE_GROUP_INSTANCE" IS '分组实例表';

COMMENT ON COLUMN "CUBE_GROUP_INSTANCE"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_GROUP_INSTANCE"."GROUP_ID" IS '分组ID';

COMMENT ON COLUMN "CUBE_GROUP_INSTANCE"."GROUP_INSTANCE_NAME" IS '分组实例名称';

COMMENT ON COLUMN "CUBE_GROUP_INSTANCE"."GROUP_INSTANCE_DESC" IS '分组实例说明';

COMMENT ON COLUMN "CUBE_GROUP_INSTANCE"."DIC_FLAG" IS '是否字典';

COMMENT ON COLUMN "CUBE_GROUP_INSTANCE"."STORAGE_TYPE" IS '存储类型';

COMMENT ON COLUMN "CUBE_GROUP_INSTANCE"."OPERATIONAL_RULE" IS '运算规则';

COMMENT ON COLUMN "CUBE_GROUP_INSTANCE"."DECIMAL_PLACES" IS '保留小数';

COMMENT ON COLUMN "CUBE_GROUP_INSTANCE"."SHOW_FORMAT" IS '显示格式';

COMMENT ON COLUMN "CUBE_GROUP_INSTANCE"."PREFIX_CHAR" IS '前缀字符';

COMMENT ON COLUMN "CUBE_GROUP_INSTANCE"."SUFFIX_CHAR" IS '后缀字符';

COMMENT ON COLUMN "CUBE_GROUP_INSTANCE"."RAW_SAMPLE" IS '原数据示例';

COMMENT ON COLUMN "CUBE_GROUP_INSTANCE"."SHOW_SAMPLE" IS '显示示例';

COMMENT ON COLUMN "CUBE_GROUP_INSTANCE"."CONTENT_ALIGN" IS '对齐方式';

COMMENT ON COLUMN "CUBE_GROUP_INSTANCE"."STATUS" IS '状态';

COMMENT ON COLUMN "CUBE_GROUP_INSTANCE"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_GROUP_INSTANCE"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_GROUP_INSTANCE"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_GROUP_INSTANCE"."UPDATE_TIME" IS '更新时间';

COMMENT ON TABLE "CUBE_IND" IS '指标表';

COMMENT ON COLUMN "CUBE_IND"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_IND"."IND_NAME" IS '指标名称';

COMMENT ON COLUMN "CUBE_IND"."IND_TYPE" IS '指标类型';

COMMENT ON COLUMN "CUBE_IND"."INDEX_NO" IS '显示序号';

COMMENT ON COLUMN "CUBE_IND"."PARENT_ID" IS '上级目录ID';

COMMENT ON COLUMN "CUBE_IND"."LEVEL_NUMBER" IS '层级数';

COMMENT ON COLUMN "CUBE_IND"."DATA_FORMAT_ID" IS '数据格式id';

COMMENT ON COLUMN "CUBE_IND"."FUNCTION_NAME" IS '聚合函数名称';

COMMENT ON COLUMN "CUBE_IND"."FUNCTION_VALUE" IS '聚合函数表达式';

COMMENT ON COLUMN "CUBE_IND"."AVG_PARMA_IND_ID" IS '加权平均指标ID';

COMMENT ON TABLE "CUBE_JOB" IS '定时任务调度表';

COMMENT ON COLUMN "CUBE_JOB"."JOB_ID" IS '任务ID';

COMMENT ON COLUMN "CUBE_JOB"."JOB_NAME" IS '任务名称';

COMMENT ON COLUMN "CUBE_JOB"."JOB_GROUP" IS '任务组名';

COMMENT ON COLUMN "CUBE_JOB"."INVOKE_TARGET" IS '调用目标字符串';

COMMENT ON COLUMN "CUBE_JOB"."CRON_EXPRESSION" IS 'cron执行表达式';

COMMENT ON COLUMN "CUBE_JOB"."MISFIRE_POLICY" IS '计划执行错误策略（1立即执行 2执行一次 3放弃执行）';

COMMENT ON COLUMN "CUBE_JOB"."CONCURRENT" IS '是否并发执行（0允许 1禁止）';

COMMENT ON COLUMN "CUBE_JOB"."STATUS" IS '状态（0正常 1暂停）';

COMMENT ON COLUMN "CUBE_JOB"."CREATE_BY" IS '创建者';

COMMENT ON COLUMN "CUBE_JOB"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_JOB"."UPDATE_BY" IS '更新者';

COMMENT ON COLUMN "CUBE_JOB"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CUBE_JOB"."REMARK" IS '备注信息';

COMMENT ON COLUMN "CUBE_JOB"."TRIGGER_FREQUENCY" IS '触发频率（1：启动应用）';

COMMENT ON TABLE "CUBE_JOB_DETAIL" IS '事件进程详情';

COMMENT ON COLUMN "CUBE_JOB_DETAIL"."ID" IS '主键';

COMMENT ON COLUMN "CUBE_JOB_DETAIL"."JOB_ID" IS '任务id';

COMMENT ON COLUMN "CUBE_JOB_DETAIL"."JOB_TYPE" IS '任务类型（1:表格进程；2:规则进程）';

COMMENT ON COLUMN "CUBE_JOB_DETAIL"."JOB_OPERATE_TYPE" IS '操作类型（1:载入源数据；2:载入备份数据；3:释放内存；4:备份至数据库；5:删除备份）';

COMMENT ON COLUMN "CUBE_JOB_DETAIL"."TABLE_ID" IS '二维表id';

COMMENT ON COLUMN "CUBE_JOB_DETAIL"."TABLE_NAME" IS '二维表名';

COMMENT ON COLUMN "CUBE_JOB_DETAIL"."DATA_VERSION" IS '数据版本';

COMMENT ON COLUMN "CUBE_JOB_DETAIL"."DATA_SOURCE" IS '数据源';

COMMENT ON COLUMN "CUBE_JOB_DETAIL"."CREATE_BY" IS '创建者';

COMMENT ON COLUMN "CUBE_JOB_DETAIL"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_JOB_DETAIL"."UPDATE_BY" IS '更新者';

COMMENT ON COLUMN "CUBE_JOB_DETAIL"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CUBE_JOB_DETAIL"."PROCESS_NAME" IS '进程名称';

COMMENT ON COLUMN "CUBE_JOB_DETAIL"."DATA_VERSION_NAME" IS '数据版本名称';

COMMENT ON TABLE "CUBE_JOB_LOG" IS '定时任务调度日志表';

COMMENT ON COLUMN "CUBE_JOB_LOG"."JOB_LOG_ID" IS '任务日志ID';

COMMENT ON COLUMN "CUBE_JOB_LOG"."JOB_NAME" IS '任务名称';

COMMENT ON COLUMN "CUBE_JOB_LOG"."JOB_GROUP" IS '任务组名';

COMMENT ON COLUMN "CUBE_JOB_LOG"."INVOKE_TARGET" IS '调用目标字符串';

COMMENT ON COLUMN "CUBE_JOB_LOG"."JOB_MESSAGE" IS '日志信息';

COMMENT ON COLUMN "CUBE_JOB_LOG"."STATUS" IS '执行状态（0正常 1失败）';

COMMENT ON COLUMN "CUBE_JOB_LOG"."EXCEPTION_INFO" IS '异常信息';

COMMENT ON COLUMN "CUBE_JOB_LOG"."JOB_ID" IS '事件id';

COMMENT ON COLUMN "CUBE_JOB_LOG"."START_TIME" IS '开始时间';

COMMENT ON COLUMN "CUBE_JOB_LOG"."STOP_TIME" IS '结束时间';

COMMENT ON TABLE "CUBE_PERMISSION" IS '数据权限配置表';

COMMENT ON COLUMN "CUBE_PERMISSION"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_PERMISSION"."ROLE_ID" IS '角色ID';

COMMENT ON COLUMN "CUBE_PERMISSION"."TABLE_ID" IS '二维表ID';

COMMENT ON COLUMN "CUBE_PERMISSION"."DATA_SCOPE" IS '数据范围';

COMMENT ON COLUMN "CUBE_PERMISSION"."TABLE_DEFINITION" IS '表格定义';

COMMENT ON COLUMN "CUBE_PERMISSION"."DATA_EDITION" IS '数据编辑';

COMMENT ON TABLE "CUBE_PERMISSION_DIM" IS '多维数据权限配置表';

COMMENT ON COLUMN "CUBE_PERMISSION_DIM"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_PERMISSION_DIM"."ROLE_ID" IS '角色ID';

COMMENT ON COLUMN "CUBE_PERMISSION_DIM"."TABLE_ID" IS '多维表ID';

COMMENT ON COLUMN "CUBE_PERMISSION_DIM"."DATA_SCOPE" IS '数据范围';

COMMENT ON COLUMN "CUBE_PERMISSION_DIM"."TABLE_DEFINITION" IS '表格定义';

COMMENT ON COLUMN "CUBE_PERMISSION_DIM"."DATA_EDITION" IS '数据编辑';

COMMENT ON TABLE "CUBE_PERMISSION_DIM_INSTANCE" IS '多维数据权限维度成员配置表';

COMMENT ON COLUMN "CUBE_PERMISSION_DIM_INSTANCE"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_PERMISSION_DIM_INSTANCE"."ROLE_ID" IS '角色ID';

COMMENT ON COLUMN "CUBE_PERMISSION_DIM_INSTANCE"."TABLE_ID" IS '二维表ID';

COMMENT ON COLUMN "CUBE_PERMISSION_DIM_INSTANCE"."DIM_DIRECTORY_ID" IS '维度目录ID';

COMMENT ON COLUMN "CUBE_PERMISSION_DIM_INSTANCE"."INSTANCE_ID" IS '维度成员ID';

COMMENT ON COLUMN "CUBE_PERMISSION_DIM_INSTANCE"."DIM_TYPE" IS '维度类型';

COMMENT ON COLUMN "CUBE_PERMISSION_DIM_INSTANCE"."DATA_SCOPE" IS '主数据维度数据范围';

COMMENT ON TABLE "CUBE_RULE" IS '计算规则';

COMMENT ON COLUMN "CUBE_RULE"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_RULE"."TABLE_ID" IS '表id';

COMMENT ON COLUMN "CUBE_RULE"."COLUMN_CODE" IS '列编码';

COMMENT ON COLUMN "CUBE_RULE"."RULE_EXPRESS" IS '规则信息';

COMMENT ON COLUMN "CUBE_RULE"."RULE_TYPE" IS '1：规则定义内容；2:规则引用关系';

COMMENT ON COLUMN "CUBE_RULE"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_RULE"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_RULE"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_RULE"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CUBE_RULE"."REF_TABLE_ID" IS '引用的表id';

COMMENT ON COLUMN "CUBE_RULE"."REF_COLUMN_CODE" IS '引用的列编码';

COMMENT ON COLUMN "CUBE_RULE"."RULE_EXPRESS_INNER" IS '规则信息(内部使用)';

COMMENT ON COLUMN "CUBE_RULE"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';

COMMENT ON TABLE "CUBE_RULE_FUNC" IS '规则函数';

COMMENT ON COLUMN "CUBE_RULE_FUNC"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_RULE_FUNC"."FUNC_NAME" IS '函数名称';

COMMENT ON COLUMN "CUBE_RULE_FUNC"."FUNC_VAL" IS '函数值';

COMMENT ON COLUMN "CUBE_RULE_FUNC"."FUNC_DESC" IS '中文描述';

COMMENT ON COLUMN "CUBE_RULE_FUNC"."FUNC_GROUP" IS '函数分组';

COMMENT ON COLUMN "CUBE_RULE_FUNC"."FUNC_SEQ" IS '函数序号';

COMMENT ON COLUMN "CUBE_RULE_FUNC"."ALIAS" IS '别名';

COMMENT ON COLUMN "CUBE_RULE_FUNC"."FUNC_SCOPE" IS '范围';

COMMENT ON TABLE "CUBE_RULE_FUNC_GROUP" IS '规则函数分组表';

COMMENT ON COLUMN "CUBE_RULE_FUNC_GROUP"."ID" IS '主键id';

COMMENT ON COLUMN "CUBE_RULE_FUNC_GROUP"."GROUP_NAME" IS '分组名称';

COMMENT ON COLUMN "CUBE_RULE_FUNC_GROUP"."GROUP_SEQ" IS '分组排序';

COMMENT ON TABLE "CUBE_SOURCE" IS '数据源表';

COMMENT ON COLUMN "CUBE_SOURCE"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_SOURCE"."SOURCE_NAME" IS '数据源名称';

COMMENT ON COLUMN "CUBE_SOURCE"."SOURCE_TYPE" IS '数据源类型';

COMMENT ON COLUMN "CUBE_SOURCE"."SOURCE_CONFIG" IS '数据源配置';

COMMENT ON COLUMN "CUBE_SOURCE"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_SOURCE"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_SOURCE"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_SOURCE"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CUBE_SOURCE"."DATA_FILE_TYPE" IS '数据文件类型';

COMMENT ON COLUMN "CUBE_SOURCE"."DATA_FILE_DIR" IS '数据文件目录';

COMMENT ON COLUMN "CUBE_SOURCE"."DIR_DATE_FORMAT" IS '目录日期格式';

COMMENT ON COLUMN "CUBE_SOURCE"."SPLITTER" IS '字段分割符';

COMMENT ON COLUMN "CUBE_SOURCE"."DATA_FILE_SUFFIX" IS '数据文件后缀';

COMMENT ON COLUMN "CUBE_SOURCE"."OK_FILE_SUFFIX" IS 'ok文件后缀';

COMMENT ON COLUMN "CUBE_SOURCE"."FILE_PATH" IS '文件路径';

COMMENT ON TABLE "CUBE_SOURCE_DETAIL" IS '数据源明细表';

COMMENT ON COLUMN "CUBE_SOURCE_DETAIL"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_SOURCE_DETAIL"."SOURCE_ID" IS '数据源id';

COMMENT ON COLUMN "CUBE_SOURCE_DETAIL"."TABLE_NAME" IS '表名称';

COMMENT ON COLUMN "CUBE_SOURCE_DETAIL"."FILE_NAME" IS '文件名称';

COMMENT ON COLUMN "CUBE_SOURCE_DETAIL"."TABLE_COMMENTS" IS '中文描述';

COMMENT ON COLUMN "CUBE_SOURCE_DETAIL"."TABLE_META" IS '表元数据';

COMMENT ON COLUMN "CUBE_SOURCE_DETAIL"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_SOURCE_DETAIL"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_SOURCE_DETAIL"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_SOURCE_DETAIL"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CUBE_TABLE_VERSION"."TABLE_ID" IS '二维表Id';

COMMENT ON COLUMN "CUBE_TABLE_VERSION"."VERSION" IS '版本名称';

COMMENT ON COLUMN "CUBE_TABLE_VERSION"."MEM_TABLE_NAME" IS '内存表名称';

COMMENT ON COLUMN "CUBE_TABLE_VERSION"."BACKUP_TABLE_NAME" IS '备份表名称';

COMMENT ON COLUMN "CUBE_TABLE_VERSION"."TABLE_META" IS '表格元数据';

COMMENT ON COLUMN "CUBE_TABLE_VERSION"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_TABLE_VERSION"."CREATE_TIME" IS '创建时间';

COMMENT ON TABLE "CUBE_VIEW" IS '数据视图表';

COMMENT ON COLUMN "CUBE_VIEW"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_VIEW"."DIRECTORY_ID" IS '目录ID';

COMMENT ON COLUMN "CUBE_VIEW"."SOURCE_ID" IS '数据源ID';

COMMENT ON COLUMN "CUBE_VIEW"."VIEW_NAME" IS '视图名称';

COMMENT ON COLUMN "CUBE_VIEW"."VIEW_SCRIPT" IS 'SQL脚本';

COMMENT ON COLUMN "CUBE_VIEW"."VIEW_META" IS '视图元数据';

COMMENT ON COLUMN "CUBE_VIEW"."STATUS" IS '状态';

COMMENT ON COLUMN "CUBE_VIEW"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_VIEW"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_VIEW"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_VIEW"."UPDATE_TIME" IS '更新时间';

COMMENT ON TABLE "CUBE_WF_DEFINE" IS '流程定义';

COMMENT ON COLUMN "CUBE_WF_DEFINE"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_WF_DEFINE"."TABLE_ID" IS '表id';

COMMENT ON COLUMN "CUBE_WF_DEFINE"."FLOW_STATE_COLUMN_NAME" IS '流程状态列名称';

COMMENT ON COLUMN "CUBE_WF_DEFINE"."FLOW_STATE_COLUMN_CODE" IS '流程状态列编码';

COMMENT ON COLUMN "CUBE_WF_DEFINE"."BATCH_SUBMIT" IS '批量提交';

COMMENT ON COLUMN "CUBE_WF_DEFINE"."FLOW_TYPE" IS '流程类型（二维、多维）';

COMMENT ON COLUMN "CUBE_WF_DEFINE"."FLOW_NAME" IS '流程名称';

COMMENT ON COLUMN "CUBE_WF_DEFINE"."PARENT_ID" IS '父Id';

COMMENT ON COLUMN "CUBE_WF_DEFINE"."TYPE" IS '类型（分组、流程）';

COMMENT ON COLUMN "CUBE_WF_DEFINE"."ANCESTORS" IS '祖级列表';

COMMENT ON COLUMN "CUBE_WF_DEFINE"."FLOWCHART_JSON" IS '流程图json';

COMMENT ON COLUMN "CUBE_WF_DEFINE"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_WF_DEFINE"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_WF_DEFINE"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_WF_DEFINE"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CUBE_WF_DEFINE"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';

COMMENT ON TABLE "CUBE_WF_DIM_DEFINE_CENTRALIZE" IS '多维流程定义归口维度';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_CENTRALIZE"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_CENTRALIZE"."DIM_DEFINE_DETAIL_ID" IS '流程定义id';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_CENTRALIZE"."CENTRALIZE_NAME" IS '归口节点名称';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_CENTRALIZE"."CENTRALIZE_DIM_ID" IS '归口节点维度id';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_CENTRALIZE"."CENTRALIZE_DIM_SIZE" IS '归口节点维度数量';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_CENTRALIZE"."USER_ID" IS '审批人id';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_CENTRALIZE"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_CENTRALIZE"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_CENTRALIZE"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_CENTRALIZE"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_CENTRALIZE"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';

COMMENT ON TABLE "CUBE_WF_DIM_DEFINE_DETAIL" IS '多维流程定义';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_DETAIL"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_DETAIL"."DEFINE_ID" IS '流程定义id';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_DETAIL"."DIM_ID" IS '维度id';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_DETAIL"."USER_ID" IS '审批人id';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_DETAIL"."CENTRALIZED_DIM_ID" IS '归口维度id';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_DETAIL"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_DETAIL"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_DETAIL"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_DETAIL"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CUBE_WF_DIM_DEFINE_DETAIL"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';

COMMENT ON TABLE "CUBE_WF_HISTORY" IS '流程历史任务';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."INSTANCE_ID" IS '流程实例id';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."DEFINE_ID" IS '流程定义id';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."TABLE_ID" IS '表id';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."TASK_ID" IS '任务id';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."DATA_ID" IS '数据id';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."COMMENT_CONTENT" IS '审批意见内容';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."NODE_ID" IS '所属节点id';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."NODE_NAME" IS '所属节点名称';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."USER_ID" IS '审批人id';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."USER_NAME" IS '审批人名称';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."NEXT_EDGE_ID" IS '提交路径id';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."NEXT_EDGE_NAME" IS '提交路径名称';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."NEXT_NODE_ID" IS '下一节点id';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."NEXT_NODE_NAME" IS '下一节点名称';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."OPERATE_TYPE" IS '任务操作类型';

COMMENT ON COLUMN "CUBE_WF_HISTORY"."CENTRALIZE_DIM_DEFINE_ID" IS '归口维度流程定义id';

COMMENT ON TABLE "CUBE_WF_INSTANCE" IS '流程运行实例';

COMMENT ON COLUMN "CUBE_WF_INSTANCE"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_WF_INSTANCE"."DEFINE_ID" IS '流程定义id';

COMMENT ON COLUMN "CUBE_WF_INSTANCE"."DEFINE_NAME" IS '流程定义名称';

COMMENT ON COLUMN "CUBE_WF_INSTANCE"."INSTANCE_STATE" IS '实例状态';

COMMENT ON COLUMN "CUBE_WF_INSTANCE"."PROCESS_STARTER" IS '发起人';

COMMENT ON COLUMN "CUBE_WF_INSTANCE"."PROCESS_STARTER_ID" IS '发起人id';

COMMENT ON COLUMN "CUBE_WF_INSTANCE"."TABLE_ID" IS '表id';

COMMENT ON COLUMN "CUBE_WF_INSTANCE"."TABLE_NAME" IS '表名';

COMMENT ON COLUMN "CUBE_WF_INSTANCE"."DATA_ID" IS '数据id';

COMMENT ON COLUMN "CUBE_WF_INSTANCE"."NODE_ID" IS '所属节点id';

COMMENT ON COLUMN "CUBE_WF_INSTANCE"."NODE_NAME" IS '所属节点名称';

COMMENT ON COLUMN "CUBE_WF_INSTANCE"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_WF_INSTANCE"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_WF_INSTANCE"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_WF_INSTANCE"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CUBE_WF_INSTANCE"."START_TIME" IS '开始时间';

COMMENT ON COLUMN "CUBE_WF_INSTANCE"."END_TIME" IS '结束时间';

COMMENT ON COLUMN "CUBE_WF_INSTANCE"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';

COMMENT ON COLUMN "CUBE_WF_INSTANCE"."FLOW_TYPE" IS '流程实例类型';

COMMENT ON TABLE "CUBE_WF_TASK" IS '流程任务';

COMMENT ON COLUMN "CUBE_WF_TASK"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_WF_TASK"."INSTANCE_ID" IS '流程实例id';

COMMENT ON COLUMN "CUBE_WF_TASK"."DEFINE_ID" IS '流程定义id';

COMMENT ON COLUMN "CUBE_WF_TASK"."DEFINE_NAME" IS '流程定义名称';

COMMENT ON COLUMN "CUBE_WF_TASK"."DATA_ID" IS '数据id';

COMMENT ON COLUMN "CUBE_WF_TASK"."TASK_DONE" IS '是否完成';

COMMENT ON COLUMN "CUBE_WF_TASK"."COST" IS '耗时（ms）';

COMMENT ON COLUMN "CUBE_WF_TASK"."PROCESS_STARTER" IS '制单人';

COMMENT ON COLUMN "CUBE_WF_TASK"."PROCESS_STARTER_ID" IS '发起人id';

COMMENT ON COLUMN "CUBE_WF_TASK"."APPROVE_ROLE_ID" IS '审批角色id';

COMMENT ON COLUMN "CUBE_WF_TASK"."APPROVE_ROLE_NAME" IS '审批角色名称';

COMMENT ON COLUMN "CUBE_WF_TASK"."NEXT_EDGE_ID" IS '提交路径id';

COMMENT ON COLUMN "CUBE_WF_TASK"."NEXT_EDGE_NAME" IS '提交路径名称';

COMMENT ON COLUMN "CUBE_WF_TASK"."NEXT_NODE_ID" IS '下一节点id';

COMMENT ON COLUMN "CUBE_WF_TASK"."NEXT_NODE_NAME" IS '下一节点名称';

COMMENT ON COLUMN "CUBE_WF_TASK"."TABLE_ID" IS '表id';

COMMENT ON COLUMN "CUBE_WF_TASK"."TABLE_NAME" IS '表名';

COMMENT ON COLUMN "CUBE_WF_TASK"."NODE_ID" IS '所属节点id';

COMMENT ON COLUMN "CUBE_WF_TASK"."NODE_NAME" IS '所属节点名称';

COMMENT ON COLUMN "CUBE_WF_TASK"."USER_ID" IS '审批人';

COMMENT ON COLUMN "CUBE_WF_TASK"."USER_NAME" IS '审批人名称';

COMMENT ON COLUMN "CUBE_WF_TASK"."COMMENT_CONTENT" IS '审批意见内容';

COMMENT ON COLUMN "CUBE_WF_TASK"."APPROVE_TIME" IS '审批时间';

COMMENT ON COLUMN "CUBE_WF_TASK"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_WF_TASK"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_WF_TASK"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_WF_TASK"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CUBE_WF_TASK"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';

COMMENT ON COLUMN "CUBE_WF_TASK"."FLOW_TYPE" IS '流程实例类型';

COMMENT ON COLUMN "CUBE_WF_TASK"."PARENT_NODE_ID" IS '父节点ID';

COMMENT ON COLUMN "CUBE_WF_TASK"."CENTRALIZE_DIM_DEFINE_ID" IS '归口维度流程定义id';

COMMENT ON COLUMN "CUBE_WF_TASK"."TASK_STATE" IS '任务状态';

COMMENT ON COLUMN "CUBE_WF_TASK"."OPERATE_USER_ID" IS '操作人';

COMMENT ON TABLE "CUBE_WF_VARIABLE" IS '流程运行变量';

COMMENT ON COLUMN "CUBE_WF_VARIABLE"."ID" IS '主键ID';

COMMENT ON COLUMN "CUBE_WF_VARIABLE"."INSTANCE_ID" IS '流程实例id';

COMMENT ON COLUMN "CUBE_WF_VARIABLE"."NODE_ID" IS '所属节点id';

COMMENT ON COLUMN "CUBE_WF_VARIABLE"."CENTRALIZE_ID" IS '归口节点id';

COMMENT ON COLUMN "CUBE_WF_VARIABLE"."VARIABLE_KEY" IS '变量key';

COMMENT ON COLUMN "CUBE_WF_VARIABLE"."VARIABLE_VAL" IS '变量值';

COMMENT ON COLUMN "CUBE_WF_VARIABLE"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_WF_VARIABLE"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_WF_VARIABLE"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_WF_VARIABLE"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CUBE_WF_VARIABLE"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';

COMMENT ON COLUMN "CUBE_WIDGET"."ID" IS '主键';

COMMENT ON COLUMN "CUBE_WIDGET"."NAME" IS '名称';

COMMENT ON COLUMN "CUBE_WIDGET"."VIEW_ID" IS '视图Id';

COMMENT ON COLUMN "CUBE_WIDGET"."VIEW_NAME" IS '视图名称';

COMMENT ON COLUMN "CUBE_WIDGET"."DASHBOARD_ID" IS 'dashboard';

COMMENT ON COLUMN "CUBE_WIDGET"."CONFIG" IS '配置';

COMMENT ON COLUMN "CUBE_WIDGET"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CUBE_WIDGET"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CUBE_WIDGET"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CUBE_WIDGET"."UPDATE_TIME" IS '更新时间';

COMMENT ON TABLE "GEN_TABLE" IS '代码生成业务表';

COMMENT ON COLUMN "GEN_TABLE"."TABLE_ID" IS '编号';

COMMENT ON COLUMN "GEN_TABLE"."TABLE_NAME" IS '表名称';

COMMENT ON COLUMN "GEN_TABLE"."TABLE_COMMENT" IS '表描述';

COMMENT ON COLUMN "GEN_TABLE"."SUB_TABLE_NAME" IS '关联子表的表名';

COMMENT ON COLUMN "GEN_TABLE"."SUB_TABLE_FK_NAME" IS '子表关联的外键名';

COMMENT ON COLUMN "GEN_TABLE"."CLASS_NAME" IS '实体类名称';

COMMENT ON COLUMN "GEN_TABLE"."TPL_CATEGORY" IS '使用的模板（crud单表操作 tree树表操作）';

COMMENT ON COLUMN "GEN_TABLE"."PACKAGE_NAME" IS '生成包路径';

COMMENT ON COLUMN "GEN_TABLE"."MODULE_NAME" IS '生成模块名';

COMMENT ON COLUMN "GEN_TABLE"."BUSINESS_NAME" IS '生成业务名';

COMMENT ON COLUMN "GEN_TABLE"."FUNCTION_NAME" IS '生成功能名';

COMMENT ON COLUMN "GEN_TABLE"."FUNCTION_AUTHOR" IS '生成功能作者';

COMMENT ON COLUMN "GEN_TABLE"."GEN_TYPE" IS '生成代码方式（0zip压缩包 1自定义路径）';

COMMENT ON COLUMN "GEN_TABLE"."GEN_PATH" IS '生成路径（不填默认项目路径）';

COMMENT ON COLUMN "GEN_TABLE"."OPTIONS" IS '其它生成选项';

COMMENT ON COLUMN "GEN_TABLE"."CREATE_BY" IS '创建者';

COMMENT ON COLUMN "GEN_TABLE"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "GEN_TABLE"."UPDATE_BY" IS '更新者';

COMMENT ON COLUMN "GEN_TABLE"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "GEN_TABLE"."REMARK" IS '备注';

COMMENT ON TABLE "GEN_TABLE_COLUMN" IS '代码生成业务表字段';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."COLUMN_ID" IS '编号';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."TABLE_ID" IS '归属表编号';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."COLUMN_NAME" IS '列名称';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."COLUMN_COMMENT" IS '列描述';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."COLUMN_TYPE" IS '列类型';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."JAVA_TYPE" IS 'JAVA类型';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."JAVA_FIELD" IS 'JAVA字段名';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_PK" IS '是否主键（1是）';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_INCREMENT" IS '是否自增（1是）';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_REQUIRED" IS '是否必填（1是）';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_INSERT" IS '是否为插入字段（1是）';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_EDIT" IS '是否编辑字段（1是）';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_LIST" IS '是否列表字段（1是）';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_QUERY" IS '是否查询字段（1是）';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."QUERY_TYPE" IS '查询方式（等于、不等于、大于、小于、范围）';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."HTML_TYPE" IS '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."DICT_TYPE" IS '字典类型';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."SORT" IS '排序';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."CREATE_BY" IS '创建者';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."UPDATE_BY" IS '更新者';

COMMENT ON COLUMN "GEN_TABLE_COLUMN"."UPDATE_TIME" IS '更新时间';

COMMENT ON TABLE "SYS_CONFIG" IS '参数配置表';

COMMENT ON COLUMN "SYS_CONFIG"."CONFIG_ID" IS '参数主键';

COMMENT ON COLUMN "SYS_CONFIG"."CONFIG_NAME" IS '参数名称';

COMMENT ON COLUMN "SYS_CONFIG"."CONFIG_KEY" IS '参数键名';

COMMENT ON COLUMN "SYS_CONFIG"."CONFIG_VALUE" IS '参数键值';

COMMENT ON COLUMN "SYS_CONFIG"."CONFIG_TYPE" IS '系统内置（Y是 N否）';

COMMENT ON COLUMN "SYS_CONFIG"."CREATE_BY" IS '创建者';

COMMENT ON COLUMN "SYS_CONFIG"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "SYS_CONFIG"."UPDATE_BY" IS '更新者';

COMMENT ON COLUMN "SYS_CONFIG"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "SYS_CONFIG"."REMARK" IS '备注';

COMMENT ON TABLE "SYS_DEPT" IS '部门表';

COMMENT ON COLUMN "SYS_DEPT"."DEPT_ID" IS '部门id';

COMMENT ON COLUMN "SYS_DEPT"."PARENT_ID" IS '父部门id';

COMMENT ON COLUMN "SYS_DEPT"."ANCESTORS" IS '祖级列表';

COMMENT ON COLUMN "SYS_DEPT"."DEPT_NAME" IS '部门名称';

COMMENT ON COLUMN "SYS_DEPT"."ORDER_NUM" IS '显示顺序';

COMMENT ON COLUMN "SYS_DEPT"."LEADER" IS '负责人';

COMMENT ON COLUMN "SYS_DEPT"."PHONE" IS '联系电话';

COMMENT ON COLUMN "SYS_DEPT"."EMAIL" IS '邮箱';

COMMENT ON COLUMN "SYS_DEPT"."STATUS" IS '部门状态（0正常 1停用）';

COMMENT ON COLUMN "SYS_DEPT"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';

COMMENT ON COLUMN "SYS_DEPT"."CREATE_BY" IS '创建者';

COMMENT ON COLUMN "SYS_DEPT"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "SYS_DEPT"."UPDATE_BY" IS '更新者';

COMMENT ON COLUMN "SYS_DEPT"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "SYS_DEPT"."DEPT_TYPE" IS '机构属性';

COMMENT ON COLUMN "SYS_DEPT"."DEPT_LEVEL" IS '机构层级';

COMMENT ON TABLE "SYS_DICT_DATA" IS '字典数据表';

COMMENT ON COLUMN "SYS_DICT_DATA"."DICT_CODE" IS '字典编码';

COMMENT ON COLUMN "SYS_DICT_DATA"."DICT_SORT" IS '字典排序';

COMMENT ON COLUMN "SYS_DICT_DATA"."DICT_LABEL" IS '字典标签';

COMMENT ON COLUMN "SYS_DICT_DATA"."DICT_VALUE" IS '字典键值';

COMMENT ON COLUMN "SYS_DICT_DATA"."DICT_TYPE" IS '字典类型';

COMMENT ON COLUMN "SYS_DICT_DATA"."CSS_CLASS" IS '样式属性（其他样式扩展）';

COMMENT ON COLUMN "SYS_DICT_DATA"."LIST_CLASS" IS '表格回显样式';

COMMENT ON COLUMN "SYS_DICT_DATA"."IS_DEFAULT" IS '是否默认（Y是 N否）';

COMMENT ON COLUMN "SYS_DICT_DATA"."STATUS" IS '状态（0正常 1停用）';

COMMENT ON COLUMN "SYS_DICT_DATA"."CREATE_BY" IS '创建者';

COMMENT ON COLUMN "SYS_DICT_DATA"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "SYS_DICT_DATA"."UPDATE_BY" IS '更新者';

COMMENT ON COLUMN "SYS_DICT_DATA"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "SYS_DICT_DATA"."REMARK" IS '备注';

COMMENT ON TABLE "SYS_DICT_TYPE" IS '字典类型表';

COMMENT ON COLUMN "SYS_DICT_TYPE"."DICT_ID" IS '字典主键';

COMMENT ON COLUMN "SYS_DICT_TYPE"."DICT_NAME" IS '字典名称';

COMMENT ON COLUMN "SYS_DICT_TYPE"."DICT_TYPE" IS '字典类型';

COMMENT ON COLUMN "SYS_DICT_TYPE"."STATUS" IS '状态（0正常 1停用）';

COMMENT ON COLUMN "SYS_DICT_TYPE"."CREATE_BY" IS '创建者';

COMMENT ON COLUMN "SYS_DICT_TYPE"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "SYS_DICT_TYPE"."UPDATE_BY" IS '更新者';

COMMENT ON COLUMN "SYS_DICT_TYPE"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "SYS_DICT_TYPE"."REMARK" IS '备注';

COMMENT ON TABLE "SYS_LOGININFOR" IS '系统访问记录';

COMMENT ON COLUMN "SYS_LOGININFOR"."INFO_ID" IS '访问ID';

COMMENT ON COLUMN "SYS_LOGININFOR"."USER_NAME" IS '用户账号';

COMMENT ON COLUMN "SYS_LOGININFOR"."IPADDR" IS '登录IP地址';

COMMENT ON COLUMN "SYS_LOGININFOR"."LOGIN_LOCATION" IS '登录地点';

COMMENT ON COLUMN "SYS_LOGININFOR"."BROWSER" IS '浏览器类型';

COMMENT ON COLUMN "SYS_LOGININFOR"."OS" IS '操作系统';

COMMENT ON COLUMN "SYS_LOGININFOR"."STATUS" IS '登录状态（0成功 1失败）';

COMMENT ON COLUMN "SYS_LOGININFOR"."MSG" IS '提示消息';

COMMENT ON COLUMN "SYS_LOGININFOR"."LOGIN_TIME" IS '访问时间';

COMMENT ON TABLE "SYS_MENU" IS '菜单权限表';

COMMENT ON COLUMN "SYS_MENU"."MENU_ID" IS '菜单ID';

COMMENT ON COLUMN "SYS_MENU"."MENU_NAME" IS '菜单名称';

COMMENT ON COLUMN "SYS_MENU"."PARENT_ID" IS '父菜单ID';

COMMENT ON COLUMN "SYS_MENU"."ORDER_NUM" IS '显示顺序';

COMMENT ON COLUMN "SYS_MENU"."PATH" IS '路由地址';

COMMENT ON COLUMN "SYS_MENU"."COMPONENT" IS '组件路径';

COMMENT ON COLUMN "SYS_MENU"."QUERY" IS '路由参数';

COMMENT ON COLUMN "SYS_MENU"."IS_FRAME" IS '是否为外链（0是 1否）';

COMMENT ON COLUMN "SYS_MENU"."IS_CACHE" IS '是否缓存（0缓存 1不缓存）';

COMMENT ON COLUMN "SYS_MENU"."MENU_TYPE" IS '菜单类型（M目录 C菜单 F按钮）';

COMMENT ON COLUMN "SYS_MENU"."VISIBLE" IS '菜单状态（0显示 1隐藏）';

COMMENT ON COLUMN "SYS_MENU"."STATUS" IS '菜单状态（0正常 1停用）';

COMMENT ON COLUMN "SYS_MENU"."PERMS" IS '权限标识';

COMMENT ON COLUMN "SYS_MENU"."ICON" IS '菜单图标';

COMMENT ON COLUMN "SYS_MENU"."CREATE_BY" IS '创建者';

COMMENT ON COLUMN "SYS_MENU"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "SYS_MENU"."UPDATE_BY" IS '更新者';

COMMENT ON COLUMN "SYS_MENU"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "SYS_MENU"."REMARK" IS '备注';

COMMENT ON TABLE "SYS_NOTICE" IS '通知公告表';

COMMENT ON COLUMN "SYS_NOTICE"."NOTICE_ID" IS '公告ID';

COMMENT ON COLUMN "SYS_NOTICE"."NOTICE_TITLE" IS '公告标题';

COMMENT ON COLUMN "SYS_NOTICE"."NOTICE_TYPE" IS '公告类型（1通知 2公告）';

COMMENT ON COLUMN "SYS_NOTICE"."NOTICE_CONTENT" IS '公告内容';

COMMENT ON COLUMN "SYS_NOTICE"."STATUS" IS '公告状态（0正常 1关闭）';

COMMENT ON COLUMN "SYS_NOTICE"."CREATE_BY" IS '创建者';

COMMENT ON COLUMN "SYS_NOTICE"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "SYS_NOTICE"."UPDATE_BY" IS '更新者';

COMMENT ON COLUMN "SYS_NOTICE"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "SYS_NOTICE"."REMARK" IS '备注';

COMMENT ON TABLE "SYS_OPER_LOG" IS '操作日志记录';

COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_ID" IS '日志主键';

COMMENT ON COLUMN "SYS_OPER_LOG"."TITLE" IS '模块标题';

COMMENT ON COLUMN "SYS_OPER_LOG"."BUSINESS_TYPE" IS '业务类型（0其它 1新增 2修改 3删除）';

COMMENT ON COLUMN "SYS_OPER_LOG"."METHOD" IS '方法名称';

COMMENT ON COLUMN "SYS_OPER_LOG"."REQUEST_METHOD" IS '请求方式';

COMMENT ON COLUMN "SYS_OPER_LOG"."OPERATOR_TYPE" IS '操作类别（0其它 1后台用户 2手机端用户）';

COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_NAME" IS '操作人员';

COMMENT ON COLUMN "SYS_OPER_LOG"."DEPT_NAME" IS '部门名称';

COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_URL" IS '请求URL';

COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_IP" IS '主机地址';

COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_LOCATION" IS '操作地点';

COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_PARAM" IS '请求参数';

COMMENT ON COLUMN "SYS_OPER_LOG"."JSON_RESULT" IS '返回参数';

COMMENT ON COLUMN "SYS_OPER_LOG"."STATUS" IS '操作状态（0正常 1异常）';

COMMENT ON COLUMN "SYS_OPER_LOG"."ERROR_MSG" IS '错误消息';

COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_TIME" IS '操作时间';

COMMENT ON TABLE "SYS_POST" IS '岗位信息表';

COMMENT ON COLUMN "SYS_POST"."POST_ID" IS '岗位ID';

COMMENT ON COLUMN "SYS_POST"."POST_CODE" IS '岗位编码';

COMMENT ON COLUMN "SYS_POST"."POST_NAME" IS '岗位名称';

COMMENT ON COLUMN "SYS_POST"."POST_SORT" IS '显示顺序';

COMMENT ON COLUMN "SYS_POST"."STATUS" IS '状态（0正常 1停用）';

COMMENT ON COLUMN "SYS_POST"."CREATE_BY" IS '创建者';

COMMENT ON COLUMN "SYS_POST"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "SYS_POST"."UPDATE_BY" IS '更新者';

COMMENT ON COLUMN "SYS_POST"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "SYS_POST"."REMARK" IS '备注';


ALTER TABLE CUBE_DIC_ITEM
    ADD CONSTRAINT PRIMARY KEY (ID);
ALTER TABLE CUBE_GROUP
    ADD PRIMARY KEY (ID);
ALTER TABLE CUBE_GROUP_INSTANCE
    ADD PRIMARY KEY (ID);
ALTER TABLE CUBE_SOURCE
    ADD PRIMARY KEY (ID);
ALTER TABLE CUBE_SOURCE_DETAIL
    ADD PRIMARY KEY (ID);
ALTER TABLE CUBE_VIEW
    ADD PRIMARY KEY (ID);

alter table CUBE_TABLE_VERSION
    add column DATA_VERSION TINYINT default 0;

comment ON COLUMN "CUBE_TABLE_VERSION"."DATA_VERSION" IS '数据版本';

CREATE TABLE CUBE_2D_TABLE_BACKUP_VERSION
(
    ID           BIGINT        NOT NULL PRIMARY KEY,
    TABLE_ID     BIGINT        NOT NULL,
    VERSION_NAME VARCHAR2(100) NOT NULL,
    SUFFIX_TYPE  CHAR(1)       NOT NULL,
    IS_OVERWRITE CHAR(1)       NOT NULL,
    CREATE_BY    VARCHAR2(64)  NOT NULL,
    CREATE_TIME  DATETIME      NOT NULL,
    UPDATE_BY    VARCHAR2(64)  NULL,
    UPDATE_TIME  DATETIME      NULL
);

COMMENT ON TABLE CUBE_2D_TABLE_BACKUP_VERSION IS '二维表备份版本信息表';

COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_VERSION.ID IS '主键ID';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_VERSION.TABLE_ID IS '二维表ID';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_VERSION.VERSION_NAME IS '版本名称';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_VERSION.SUFFIX_TYPE IS '后缀类型';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_VERSION.IS_OVERWRITE IS '是否自动覆盖同名备份';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_VERSION.CREATE_BY IS '创建人';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_VERSION.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_VERSION.UPDATE_BY IS '更新人';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_VERSION.UPDATE_TIME IS '更新时间';


CREATE TABLE CUBE_2D_TABLE_BACKUP_CONFIG
(
    ID                       BIGINT        NOT NULL PRIMARY KEY,
    TABLE_ID                 BIGINT        NOT NULL,
    BACKUP_TABLE_NAME        VARCHAR2(100) NULL,
    BACKUP_TABLE_NAME_PREFIX VARCHAR2(10)  NULL,
    COLUMN_CODE              VARCHAR2(100) NULL,
    BACKUP_COLUMN_NAME       VARCHAR2(100) NULL,
    COLUMN_TYPE              VARCHAR2(10)  NULL,
    CONFIG_TYPE              CHAR(1)       NOT NULL,
    COLUMN_LENGTH            INT           NULL,
    CREATE_BY                VARCHAR2(64)  NOT NULL,
    CREATE_TIME              DATETIME      NOT NULL,
    UPDATE_BY                VARCHAR2(64)  NULL,
    UPDATE_TIME              DATETIME      NULL
);

COMMENT ON TABLE CUBE_2D_TABLE_BACKUP_CONFIG IS '二维表备份版本配置表';

COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_CONFIG.ID IS '主键ID';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_CONFIG.TABLE_ID IS '二维表ID';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_CONFIG.BACKUP_TABLE_NAME IS '备份表名';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_CONFIG.BACKUP_TABLE_NAME_PREFIX IS '备份表名前缀';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_CONFIG.COLUMN_CODE IS '列编码';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_CONFIG.BACKUP_COLUMN_NAME IS '备份字段名';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_CONFIG.COLUMN_TYPE IS '字段类型';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_CONFIG.CONFIG_TYPE IS '配置类型（0表；1列）';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_CONFIG.COLUMN_LENGTH IS '字段长度';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_CONFIG.CREATE_BY IS '创建人';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_CONFIG.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_CONFIG.UPDATE_BY IS '更新人';
COMMENT ON COLUMN CUBE_2D_TABLE_BACKUP_CONFIG.UPDATE_TIME IS '更新时间';

CREATE INDEX CUBE_2D_TABLE_BACKUP_VERSION_TABLE_ID_INDEX
    ON CUBE_2D_TABLE_BACKUP_VERSION (TABLE_ID);

CREATE INDEX CUBE_2D_TABLE_BACKUP_CONFIG_TABLE_ID_INDEX
    ON CUBE_2D_TABLE_BACKUP_CONFIG (TABLE_ID);

COMMENT
ON TABLE CUBE_DASHBOARD IS '可视化仪表盘表';
COMMENT
ON TABLE CUBE_TABLE_VERSION IS '备份版本表';
COMMENT
ON TABLE CUBE_WIDGET IS '可视化组件表';

COMMENT
ON COLUMN CUBE_2D_TABLE.TABLE_META IS '表格元数据';
COMMENT
ON COLUMN CUBE_2D_TABLE.PARENT_ID IS '父Id';
COMMENT
ON COLUMN CUBE_2D_TABLE.TYPE IS '类型';
COMMENT
ON COLUMN CUBE_2D_TABLE.ANCESTORS IS '祖级列表';
COMMENT
ON COLUMN CUBE_2D_TABLE.MEM_TABLE_NAME IS '内存数据库表名';

COMMENT
ON COLUMN CUBE_TABLE_VERSION.ID IS '主键ID';
COMMENT
ON COLUMN CUBE_WIDGET.SEQ IS '序号';


alter table CUBE_2D_TABLE modify ANCESTORS VARCHAR (512);
alter table CUBE_DIM_TABLE modify ANCESTORS VARCHAR (512);
alter table CUBE_SYS_DEPT modify ANCESTORS VARCHAR (512);
alter table CUBE_WF_DEFINE modify ANCESTORS VARCHAR (512);
COMMENT
ON COLUMN CUBE_DIM_INSTANCE.ANCESTORS IS '祖级列表';
COMMENT
ON COLUMN CUBE_DIM_TABLE.ANCESTORS IS '祖级列表';

alter table CUBE_TABLE_VERSION modify BACKUP_TABLE_NAME VARCHAR2(100);
COMMENT
ON COLUMN CUBE_TABLE_VERSION.BACKUP_TABLE_NAME IS '备份表名';

COMMENT
ON COLUMN CUBE_WF_DIM_DEFINE_DETAIL.CENTRALIZED_DIM_ID IS '归口节点维度id';

alter table CUBE_RULE modify COLUMN_CODE VARCHAR2(100);

alter table CUBE_2D_TABLE_BACKUP_CONFIG modify CREATE_BY VARCHAR (64);
alter table CUBE_2D_TABLE_BACKUP_VERSION modify CREATE_BY VARCHAR (64);

COMMENT
ON COLUMN CUBE_JOB.CREATE_BY IS '创建人';
COMMENT
ON COLUMN CUBE_SYS_DICT_TYPE.CREATE_BY IS '创建人';
COMMENT
ON COLUMN CUBE_SYS_USER.CREATE_BY IS '创建人';
COMMENT
ON COLUMN CUBE_SYS_MENU.CREATE_BY IS '创建人';
COMMENT
ON COLUMN CUBE_SYS_NOTICE.CREATE_BY IS '创建人';
COMMENT
ON COLUMN CUBE_SYS_DEPT.CREATE_BY IS '创建人';
COMMENT
ON COLUMN CUBE_JOB_DETAIL.CREATE_BY IS '创建人';
COMMENT
ON COLUMN CUBE_SYS_CONFIG.CREATE_BY IS '创建人';
COMMENT
ON COLUMN CUBE_SYS_DICT_DATA.CREATE_BY IS '创建人';
COMMENT
ON COLUMN CUBE_SYS_ROLE.CREATE_BY IS '创建人';
COMMENT
ON COLUMN CUBE_SYS_POST.CREATE_BY IS '创建人';

COMMENT
ON COLUMN CUBE_TABLE_VERSION.DATA_VERSION IS '数据版本';

COMMENT
ON COLUMN CUBE_DASHBOARD.DEPT_ID IS '部门ID';
COMMENT
ON COLUMN CUBE_SYS_DEPT.DEPT_ID IS '部门ID';

alter table CUBE_SYS_DEPT modify DEPT_NAME VARCHAR (50);


COMMENT
ON COLUMN CUBE_DIM_RULE_INDICATOR_REF.DIM_RULE_ID IS '多维表规则ID';
COMMENT
ON COLUMN CUBE_DIM_RULE_INDICATOR_REF.DIM_TABLE_ID IS '多维表ID';

COMMENT
ON COLUMN CUBE_SYS_USER.EMAIL IS '邮箱';
COMMENT
ON COLUMN CUBE_DIM_LAYOUT_FILTER.FILTER_DIM IS '筛选维度列表';

alter table CUBE_GROUP modify GROUP_NAME VARCHAR (255);

COMMENT
ON COLUMN CUBE_DIM_RULE_INDICATOR_REF.IND_ID IS '指标ID';

alter table CUBE_DIM_RULE_INDICATOR_OPERATION modify IND_NAME VARCHAR (128);

COMMENT
ON COLUMN CUBE_JOB_LOG.JOB_ID IS '任务ID';
COMMENT
ON COLUMN CUBE_JOB_DETAIL.JOB_ID IS '任务ID';

COMMENT
ON COLUMN CUBE_TABLE_VERSION.MEM_TABLE_NAME IS '内存数据库表名';
COMMENT
ON COLUMN CUBE_DIM_TABLE.MEM_TABLE_NAME IS '内存数据库表名';

COMMENT
ON COLUMN CUBE_DIM_RULE.ORDER_NUM IS '显示顺序';

COMMENT
ON COLUMN CUBE_WF_TASK.PROCESS_STARTER IS '发起人';

COMMENT
ON COLUMN CUBE_JOB.REMARK IS '备注';
alter table CUBE_SYS_NOTICE modify REMARK VARCHAR (500);

COMMENT
ON COLUMN CUBE_SOURCE_DETAIL.SOURCE_ID IS '数据源ID';



drop table CUBE_JOB;
CREATE TABLE "CUBE_JOB"
(
    "JOB_ID" BIGINT NOT NULL,
    "JOB_NAME"          VARCHAR2(64)  DEFAULT ''
                                     NOT NULL,
    "JOB_GROUP"         VARCHAR2(64)  DEFAULT 'DEFAULT'
                                     NOT NULL,
    "INVOKE_TARGET"     VARCHAR2(500) NOT NULL,
    "CRON_EXPRESSION"   VARCHAR2(255) DEFAULT ''
        NULL,
    "MISFIRE_POLICY"    VARCHAR2(20)  DEFAULT '3'
        NULL,
    "CONCURRENT"        CHAR(1) DEFAULT '1'
        NULL,
    "STATUS"            CHAR(1) DEFAULT '0'
        NULL,
    "CREATE_BY"         VARCHAR2(64)  DEFAULT ''
        NULL,
    "CREATE_TIME"       TIMESTAMP(0) NULL,
    "UPDATE_BY"         VARCHAR2(64) DEFAULT ''
        NULL,
    "UPDATE_TIME"       TIMESTAMP(0) NULL,
    "REMARK"            VARCHAR2(500) DEFAULT ''
        NULL,
    "TRIGGER_FREQUENCY" CHAR(1) DEFAULT '0'
        NULL
);
ALTER TABLE "CUBE_JOB"
    ADD CONSTRAINT PRIMARY KEY ("JOB_ID", "JOB_NAME", "JOB_GROUP");

COMMENT
ON TABLE "CUBE_JOB" IS '定时任务调度表';

COMMENT
ON COLUMN "CUBE_JOB"."JOB_ID" IS '任务ID';

COMMENT
ON COLUMN "CUBE_JOB"."JOB_NAME" IS '任务名称';

COMMENT
ON COLUMN "CUBE_JOB"."JOB_GROUP" IS '任务组名';

COMMENT
ON COLUMN "CUBE_JOB"."INVOKE_TARGET" IS '调用目标字符串';

COMMENT
ON COLUMN "CUBE_JOB"."CRON_EXPRESSION" IS 'cron执行表达式';

COMMENT
ON COLUMN "CUBE_JOB"."MISFIRE_POLICY" IS '计划执行错误策略（1立即执行 2执行一次 3放弃执行）';

COMMENT
ON COLUMN "CUBE_JOB"."CONCURRENT" IS '是否并发执行（0允许 1禁止）';

COMMENT
ON COLUMN "CUBE_JOB"."STATUS" IS '状态（0正常 1暂停）';

COMMENT
ON COLUMN "CUBE_JOB"."CREATE_BY" IS '创建者';

COMMENT
ON COLUMN "CUBE_JOB"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "CUBE_JOB"."UPDATE_BY" IS '更新人';

COMMENT
ON COLUMN "CUBE_JOB"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "CUBE_JOB"."REMARK" IS '备注信息';

COMMENT
ON COLUMN "CUBE_JOB"."TRIGGER_FREQUENCY" IS '触发频率（1：启动应用）';

COMMENT
ON COLUMN CUBE_JOB.CREATE_BY IS '创建人';
        COMMENT
ON COLUMN CUBE_JOB.REMARK IS '备注';

alter table CUBE_RULE_FUNC modify ALIAS VARCHAR2(50);
alter table CUBE_2D_TABLE modify ANCESTORS VARCHAR2(512);
alter table CUBE_DIM_INSTANCE modify ANCESTORS VARCHAR2(512);
alter table CUBE_DIM_TABLE modify ANCESTORS VARCHAR2(512);
alter table CUBE_WF_DEFINE modify ANCESTORS VARCHAR2(512);
alter table CUBE_SYS_DEPT modify ANCESTORS VARCHAR2(512);
alter table CUBE_WF_TASK modify APPROVE_ROLE_ID VARCHAR2(200);
alter table CUBE_WF_TASK modify APPROVE_ROLE_NAME VARCHAR2(500);
alter table CUBE_SYS_USER modify AVATAR VARCHAR2(100);
alter table CUBE_IND modify AVG_PARMA_IND_ID VARCHAR2(32);
alter table CUBE_SYS_LOGININFOR modify BROWSER VARCHAR2(50);
alter table CUBE_WF_DIM_DEFINE_DETAIL modify CENTRALIZED_DIM_ID VARCHAR2(200);
alter table CUBE_WF_VARIABLE modify CENTRALIZE_ID VARCHAR2(100);
alter table CUBE_WF_DIM_DEFINE_CENTRALIZE modify CENTRALIZE_NAME VARCHAR2(64);
alter table CUBE_2D_TABLE modify CHILD_FLAG VARCHAR2(32);
alter table CUBE_DIM_LAYOUT modify COLUMN_DIM VARCHAR2(255);
alter table CUBE_WF_HISTORY modify COMMENT_CONTENT VARCHAR2(500);
alter table CUBE_WF_TASK modify COMMENT_CONTENT VARCHAR2(500);
alter table CUBE_SYS_MENU modify COMPONENT VARCHAR2(255);
alter table CUBE_2D_TABLE_GEN modify CONFIG VARCHAR2(8188);
alter table CUBE_SYS_CONFIG modify CONFIG_KEY VARCHAR2(100);
alter table CUBE_SYS_CONFIG modify CONFIG_NAME VARCHAR2(100);
alter table CUBE_SYS_CONFIG modify CONFIG_VALUE VARCHAR2(500);
alter table CUBE_GROUP_INSTANCE modify CONTENT_ALIGN VARCHAR2(255);
alter table CUBE_SYS_DEPT modify CREATE_BY VARCHAR2(64);
alter table CUBE_WF_HISTORY modify CREATE_BY VARCHAR2(64);
alter table CUBE_2D_TABLE_BACKUP_CONFIG modify CREATE_BY VARCHAR2(64);
alter table CUBE_WF_DIM_DEFINE_DETAIL modify CREATE_BY VARCHAR2(64);
alter table CUBE_WF_DIM_DEFINE_CENTRALIZE modify CREATE_BY VARCHAR2(64);
alter table CUBE_WF_INSTANCE modify CREATE_BY VARCHAR2(64);
alter table CUBE_SYS_NOTICE modify CREATE_BY VARCHAR2(64);
alter table CUBE_WF_VARIABLE modify CREATE_BY VARCHAR2(64);
alter table CUBE_TABLE_VERSION modify CREATE_BY VARCHAR2(64);
alter table CUBE_2D_TABLE_BACKUP_VERSION modify CREATE_BY VARCHAR2(64);
alter table CUBE_DIM_TABLE modify CREATE_BY VARCHAR2(64);
alter table CUBE_DIM_RULE_INDICATOR_REF modify CREATE_BY VARCHAR2(64);
alter table CUBE_SOURCE_DETAIL modify CREATE_BY VARCHAR2(64);
alter table CUBE_WF_DEFINE modify CREATE_BY VARCHAR2(64);
alter table CUBE_SOURCE modify CREATE_BY VARCHAR2(64);
alter table CUBE_GROUP modify CREATE_BY VARCHAR2(64);
alter table CUBE_RULE modify CREATE_BY VARCHAR2(64);
alter table CUBE_SYS_CONFIG modify CREATE_BY VARCHAR2(64);
alter table CUBE_SYS_USER modify CREATE_BY VARCHAR2(64);
alter table CUBE_SYS_DICT_DATA modify CREATE_BY VARCHAR2(64);
alter table CUBE_DIM_RULE modify CREATE_BY VARCHAR2(64);
alter table CUBE_DIM_RULE_INDICATOR_OPERATION modify CREATE_BY VARCHAR2(64);
alter table CUBE_WF_TASK modify CREATE_BY VARCHAR2(64);
alter table CUBE_DIC_ITEM modify CREATE_BY VARCHAR2(64);
alter table CUBE_SYS_ROLE modify CREATE_BY VARCHAR2(64);
alter table CUBE_SYS_MENU modify CREATE_BY VARCHAR2(64);
alter table CUBE_GROUP_INSTANCE modify CREATE_BY VARCHAR2(64);
alter table CUBE_DASHBOARD modify CREATE_BY VARCHAR2(64);
alter table CUBE_SYS_DICT_TYPE modify CREATE_BY VARCHAR2(64);
alter table CUBE_JOB_DETAIL modify CREATE_BY VARCHAR2(64);
alter table CUBE_SYS_POST modify CREATE_BY VARCHAR2(64);
alter table CUBE_WIDGET modify CREATE_BY VARCHAR2(64);
alter table CUBE_2D_TABLE modify CREATE_BY VARCHAR2(64);
alter table CUBE_VIEW modify CREATE_BY VARCHAR2(64);
alter table CUBE_2D_TABLE modify CREATE_MODE VARCHAR2(32);
alter table CUBE_DIM_TABLE modify CREATE_MODE VARCHAR2(32);
alter table CUBE_SYS_DICT_DATA modify CSS_CLASS VARCHAR2(100);
alter table CUBE_WIDGET modify DASHBOARD_ID VARCHAR2(32);
alter table CUBE_SOURCE modify DATA_FILE_DIR VARCHAR2(100);
alter table CUBE_SOURCE modify DATA_FILE_SUFFIX VARCHAR2(50);
alter table CUBE_SOURCE modify DATA_FILE_TYPE VARCHAR2(100);
alter table CUBE_IND modify DATA_FORMAT_ID VARCHAR2(32);
alter table CUBE_JOB_DETAIL modify DATA_SOURCE VARCHAR2(255);
alter table CUBE_JOB_DETAIL modify DATA_VERSION VARCHAR2(255);
alter table CUBE_JOB_DETAIL modify DATA_VERSION_NAME VARCHAR2(255);
alter table CUBE_WF_INSTANCE modify DEFINE_NAME VARCHAR2(200);
alter table CUBE_WF_TASK modify DEFINE_NAME VARCHAR2(200);
alter table CUBE_SYS_DEPT modify DEPT_LEVEL VARCHAR2(100);
alter table CUBE_SYS_OPER_LOG modify DEPT_NAME VARCHAR2(50);
alter table CUBE_SYS_DEPT modify DEPT_NAME VARCHAR2(50);
alter table CUBE_SYS_DEPT modify DEPT_TYPE VARCHAR2(100);
alter table CUBE_SYS_DICT_DATA modify DICT_LABEL VARCHAR2(100);
alter table CUBE_SYS_DICT_TYPE modify DICT_NAME VARCHAR2(100);
alter table CUBE_SYS_DICT_DATA modify DICT_TYPE VARCHAR2(100);
alter table CUBE_SYS_DICT_TYPE modify DICT_TYPE VARCHAR2(100);
alter table CUBE_SYS_DICT_DATA modify DICT_VALUE VARCHAR2(100);
alter table CUBE_DIC_ITEM modify DIC_CODE VARCHAR2(64);
alter table CUBE_GROUP_INSTANCE modify DIC_FLAG VARCHAR2(32);
alter table CUBE_DIC_ITEM modify DIC_ID VARCHAR2(32);
alter table CUBE_DIC_ITEM modify DIC_LABEL VARCHAR2(64);
alter table CUBE_DIM_INSTANCE modify DIM_CODE VARCHAR2(64);
alter table CUBE_DIM_RULE_INDICATOR_OPERATION modify DIM_DIRECTORY_NAME VARCHAR2(100);
alter table CUBE_DIM_DIRECTORY modify DIM_DIRECTORY_NAME VARCHAR2(128);
alter table CUBE_DIM_DIRECTORY modify DIM_DIRECTORY_TYPE VARCHAR2(32);
alter table CUBE_DIM_INSTANCE modify DIM_NAME VARCHAR2(128);
alter table CUBE_DIM_DIRECTORY modify DIM_TYPE VARCHAR2(32);
alter table CUBE_PERMISSION_DIM_INSTANCE modify DIM_TYPE VARCHAR2(32);
alter table CUBE_2D_TABLE modify DIRECTORY_ID VARCHAR2(32);
alter table CUBE_VIEW modify DIRECTORY_ID VARCHAR2(32);
alter table CUBE_SOURCE modify DIR_DATE_FORMAT VARCHAR2(20);
alter table CUBE_SYS_DEPT modify EMAIL VARCHAR2(50);
alter table CUBE_SYS_USER modify EMAIL VARCHAR2(50);
alter table CUBE_SYS_OPER_LOG modify ERROR_MSG VARCHAR2(8188);
alter table CUBE_JOB_LOG modify EXCEPTION_INFO VARCHAR2(2000);
alter table CUBE_SOURCE_DETAIL modify FILE_NAME VARCHAR2(64);
alter table CUBE_SOURCE modify FILE_PATH VARCHAR2(100);
alter table CUBE_DIM_LAYOUT_FILTER modify FILTER_DIM VARCHAR2(255);
alter table CUBE_DIM_LAYOUT modify FILTER_DIM VARCHAR2(255);
alter table CUBE_DIM_LAYOUT_FILTER modify FILTER_VALUES VARCHAR2(512);
alter table CUBE_WF_DEFINE modify FLOW_NAME VARCHAR2(200);
alter table CUBE_WF_DEFINE modify FLOW_STATE_COLUMN_CODE VARCHAR2(200);
alter table CUBE_WF_DEFINE modify FLOW_STATE_COLUMN_NAME VARCHAR2(200);
alter table CUBE_WF_DEFINE modify FLOW_TYPE VARCHAR2(2);
alter table CUBE_IND modify FUNCTION_NAME VARCHAR2(128);
alter table CUBE_IND modify FUNCTION_VALUE VARCHAR2(128);
alter table CUBE_RULE_FUNC modify FUNC_DESC VARCHAR2(8188);
alter table CUBE_RULE_FUNC modify FUNC_NAME VARCHAR2(255);
alter table CUBE_RULE_FUNC modify FUNC_SCOPE VARCHAR2(10);
alter table CUBE_RULE_FUNC modify FUNC_VAL VARCHAR2(255);
alter table CUBE_2D_TABLE_GEN modify GEN_TYPE VARCHAR2(32);
alter table CUBE_GROUP modify GROUP_CODE VARCHAR2(64);
alter table CUBE_GROUP_INSTANCE modify GROUP_ID VARCHAR2(32);
alter table CUBE_GROUP_INSTANCE modify GROUP_INSTANCE_DESC VARCHAR2(128);
alter table CUBE_GROUP_INSTANCE modify GROUP_INSTANCE_NAME VARCHAR2(64);
alter table CUBE_RULE_FUNC_GROUP modify GROUP_NAME VARCHAR2(255);
alter table CUBE_GROUP modify GROUP_NAME VARCHAR2(255);
alter table CUBE_SYS_MENU modify ICON VARCHAR2(100);
alter table CUBE_DIM_RULE_INDICATOR_OPERATION modify IND_NAME VARCHAR2(128);
alter table CUBE_IND modify IND_NAME VARCHAR2(128);
alter table CUBE_IND modify IND_TYPE VARCHAR2(32);
alter table CUBE_JOB_LOG modify INVOKE_TARGET VARCHAR2(500);
alter table CUBE_SYS_LOGININFOR modify IPADDR VARCHAR2(128);
alter table CUBE_DIM_TABLE_REL modify IS_DEFAULT_COLUMN_DIM VARCHAR2(32);
alter table CUBE_DIM_TABLE_REL modify IS_DEFAULT_DIM VARCHAR2(32);
alter table CUBE_DIM_INSTANCE modify IS_LEAF VARCHAR2(8);
alter table CUBE_JOB_LOG modify JOB_GROUP VARCHAR2(64);
alter table CUBE_JOB_LOG modify JOB_MESSAGE VARCHAR2(500);
alter table CUBE_JOB_LOG modify JOB_NAME VARCHAR2(64);
alter table CUBE_SYS_OPER_LOG modify JSON_RESULT VARCHAR2(8188);
alter table CUBE_SYS_DEPT modify LEADER VARCHAR2(20);
alter table CUBE_SYS_DICT_DATA modify LIST_CLASS VARCHAR2(100);
alter table CUBE_SYS_USER modify LOGIN_IP VARCHAR2(128);
alter table CUBE_SYS_LOGININFOR modify LOGIN_LOCATION VARCHAR2(255);
alter table CUBE_2D_TABLE modify MEMSIZE VARCHAR2(50);
alter table CUBE_DIM_TABLE modify MEM_TABLE_NAME VARCHAR2(64);
alter table CUBE_TABLE_VERSION modify MEM_TABLE_NAME VARCHAR2(64);
alter table CUBE_2D_TABLE modify MEM_TABLE_NAME VARCHAR2(64);
alter table CUBE_SYS_MENU modify MENU_NAME VARCHAR2(50);
alter table CUBE_SYS_OPER_LOG modify METHOD VARCHAR2(100);
alter table CUBE_SYS_LOGININFOR modify MSG VARCHAR2(255);
alter table CUBE_WIDGET modify NAME VARCHAR2(255);
alter table CUBE_DASHBOARD modify NAME VARCHAR2(255);
alter table CUBE_WF_TASK modify NEXT_EDGE_ID VARCHAR2(64);
alter table CUBE_WF_HISTORY modify NEXT_EDGE_ID VARCHAR2(64);
alter table CUBE_WF_TASK modify NEXT_EDGE_NAME VARCHAR2(64);
alter table CUBE_WF_HISTORY modify NEXT_EDGE_NAME VARCHAR2(64);
alter table CUBE_WF_TASK modify NEXT_NODE_ID VARCHAR2(64);
alter table CUBE_WF_HISTORY modify NEXT_NODE_ID VARCHAR2(64);
alter table CUBE_WF_TASK modify NEXT_NODE_NAME VARCHAR2(64);
alter table CUBE_WF_HISTORY modify NEXT_NODE_NAME VARCHAR2(64);
alter table CUBE_SYS_USER modify NICK_NAME VARCHAR2(30);
alter table CUBE_WF_VARIABLE modify NODE_ID VARCHAR2(100);
alter table CUBE_WF_INSTANCE modify NODE_ID VARCHAR2(100);
alter table CUBE_WF_HISTORY modify NODE_ID VARCHAR2(100);
alter table CUBE_WF_TASK modify NODE_ID VARCHAR2(100);
alter table CUBE_WF_TASK modify NODE_NAME VARCHAR2(64);
alter table CUBE_WF_INSTANCE modify NODE_NAME VARCHAR2(64);
alter table CUBE_WF_HISTORY modify NODE_NAME VARCHAR2(64);
alter table CUBE_SYS_NOTICE modify NOTICE_TITLE VARCHAR2(50);
alter table CUBE_SOURCE modify OK_FILE_SUFFIX VARCHAR2(20);
alter table CUBE_WF_TASK modify OPERATE_USER_ID VARCHAR2(64);
alter table CUBE_GROUP_INSTANCE modify OPERATIONAL_RULE VARCHAR2(64);
alter table CUBE_SYS_OPER_LOG modify OPER_IP VARCHAR2(128);
alter table CUBE_SYS_OPER_LOG modify OPER_LOCATION VARCHAR2(255);
alter table CUBE_SYS_OPER_LOG modify OPER_NAME VARCHAR2(50);
alter table CUBE_SYS_OPER_LOG modify OPER_PARAM VARCHAR2(8188);
alter table CUBE_SYS_OPER_LOG modify OPER_URL VARCHAR2(255);
alter table CUBE_SYS_LOGININFOR modify OS VARCHAR2(50);
alter table CUBE_DASHBOARD modify PARENT_ID VARCHAR2(32);
alter table CUBE_WF_TASK modify PARENT_NODE_ID VARCHAR2(100);
alter table CUBE_SYS_USER modify PASSWORD VARCHAR2(100);
alter table CUBE_SYS_MENU modify PATH VARCHAR2(200);
alter table CUBE_SYS_MENU modify PERMS VARCHAR2(100);
alter table CUBE_SYS_DEPT modify PHONE VARCHAR2(11);
alter table CUBE_SYS_USER modify PHONENUMBER VARCHAR2(11);
alter table CUBE_SYS_POST modify POST_CODE VARCHAR2(64);
alter table CUBE_SYS_POST modify POST_NAME VARCHAR2(50);
alter table CUBE_GROUP_INSTANCE modify PREFIX_CHAR VARCHAR2(32);
alter table CUBE_JOB_DETAIL modify PROCESS_NAME VARCHAR2(255);
alter table CUBE_WF_TASK modify PROCESS_STARTER VARCHAR2(128);
alter table CUBE_WF_INSTANCE modify PROCESS_STARTER VARCHAR2(128);
alter table CUBE_WF_TASK modify PROCESS_STARTER_ID VARCHAR2(64);
alter table CUBE_WF_INSTANCE modify PROCESS_STARTER_ID VARCHAR2(64);
alter table CUBE_SYS_MENU modify QUERY VARCHAR2(255);
alter table CUBE_GROUP_INSTANCE modify RAW_SAMPLE VARCHAR2(255);
alter table CUBE_RULE modify REF_COLUMN_CODE VARCHAR2(32);
alter table CUBE_DIM_RULE_INDICATOR_REF modify REF_IND_ID VARCHAR2(64);
alter table CUBE_2D_TABLE_REL modify REL_TABLE_CODE VARCHAR2(32);
alter table CUBE_2D_TABLE_REL modify REL_TABLE_NAME VARCHAR2(64);
alter table CUBE_DIM_TABLE_REL modify REL_TYPE VARCHAR2(32);
alter table CUBE_SYS_USER modify REMARK VARCHAR2(500);
alter table CUBE_SYS_ROLE modify REMARK VARCHAR2(500);
alter table CUBE_SYS_POST modify REMARK VARCHAR2(500);
alter table CUBE_SYS_NOTICE modify REMARK VARCHAR2(500);
alter table CUBE_SYS_MENU modify REMARK VARCHAR2(500);
alter table CUBE_SYS_DICT_TYPE modify REMARK VARCHAR2(500);
alter table CUBE_SYS_DICT_DATA modify REMARK VARCHAR2(500);
alter table CUBE_SYS_CONFIG modify REMARK VARCHAR2(500);
alter table CUBE_SYS_OPER_LOG modify REQUEST_METHOD VARCHAR2(10);
alter table CUBE_PERMISSION_DIM_INSTANCE modify ROLE_ID VARCHAR2(32);
alter table CUBE_PERMISSION_DIM modify ROLE_ID VARCHAR2(32);
alter table CUBE_PERMISSION modify ROLE_ID VARCHAR2(32);
alter table CUBE_SYS_ROLE modify ROLE_KEY VARCHAR2(100);
alter table CUBE_SYS_ROLE modify ROLE_NAME VARCHAR2(30);
alter table CUBE_DIM_LAYOUT modify ROW_DIM VARCHAR2(255);
alter table CUBE_DIM_RULE modify RULE_NAME VARCHAR2(100);
alter table CUBE_GROUP_INSTANCE modify SHOW_FORMAT VARCHAR2(32);
alter table CUBE_GROUP_INSTANCE modify SHOW_SAMPLE VARCHAR2(255);
alter table CUBE_VIEW modify SOURCE_ID VARCHAR2(32);
alter table CUBE_SOURCE_DETAIL modify SOURCE_ID VARCHAR2(32);
alter table CUBE_SOURCE modify SOURCE_NAME VARCHAR2(64);
alter table CUBE_SOURCE modify SOURCE_TYPE VARCHAR2(32);
alter table CUBE_SOURCE modify SPLITTER VARCHAR2(20);
alter table CUBE_DIC_ITEM modify STATUS VARCHAR2(32);
alter table CUBE_VIEW modify STATUS VARCHAR2(32);
alter table CUBE_DASHBOARD modify STATUS VARCHAR2(32);
alter table CUBE_GROUP_INSTANCE modify STATUS VARCHAR2(32);
alter table CUBE_GROUP modify STATUS VARCHAR2(32);
alter table CUBE_2D_TABLE modify STATUS VARCHAR2(32);
alter table CUBE_GROUP_INSTANCE modify STORAGE_TYPE VARCHAR2(32);
alter table CUBE_GROUP_INSTANCE modify SUFFIX_CHAR VARCHAR2(32);
alter table CUBE_SOURCE_DETAIL modify TABLE_COMMENTS VARCHAR2(100);
alter table CUBE_PERMISSION_DIM_INSTANCE modify TABLE_ID VARCHAR2(32);
alter table CUBE_PERMISSION_DIM modify TABLE_ID VARCHAR2(32);
alter table CUBE_PERMISSION modify TABLE_ID VARCHAR2(32);
alter table CUBE_JOB_DETAIL modify TABLE_NAME VARCHAR2(255);
alter table CUBE_2D_TABLE modify TABLE_NAME VARCHAR2(64);
alter table CUBE_SOURCE_DETAIL modify TABLE_NAME VARCHAR2(64);
alter table CUBE_WF_TASK modify TABLE_NAME VARCHAR2(64);
alter table CUBE_WF_INSTANCE modify TABLE_NAME VARCHAR2(64);
alter table CUBE_DIM_TABLE modify TABLE_NAME VARCHAR2(64);
alter table CUBE_SYS_OPER_LOG modify TITLE VARCHAR2(50);
alter table CUBE_WF_DEFINE modify TYPE VARCHAR2(2);
alter table CUBE_DIM_TABLE modify TYPE VARCHAR2(32);
alter table CUBE_2D_TABLE modify TYPE VARCHAR2(32);
alter table CUBE_DASHBOARD modify TYPE VARCHAR2(30);
alter table CUBE_RULE modify UPDATE_BY VARCHAR2(64);
alter table CUBE_WF_DIM_DEFINE_DETAIL modify UPDATE_BY VARCHAR2(64);
alter table CUBE_WF_DIM_DEFINE_CENTRALIZE modify UPDATE_BY VARCHAR2(64);
alter table CUBE_WF_DEFINE modify UPDATE_BY VARCHAR2(64);
alter table CUBE_2D_TABLE modify UPDATE_BY VARCHAR2(64);
alter table CUBE_VIEW modify UPDATE_BY VARCHAR2(64);
alter table CUBE_DIM_TABLE modify UPDATE_BY VARCHAR2(64);
alter table CUBE_SYS_USER modify UPDATE_BY VARCHAR2(64);
alter table CUBE_SYS_ROLE modify UPDATE_BY VARCHAR2(64);
alter table CUBE_DIM_RULE_INDICATOR_REF modify UPDATE_BY VARCHAR2(64);
alter table CUBE_DIC_ITEM modify UPDATE_BY VARCHAR2(64);
alter table CUBE_SYS_POST modify UPDATE_BY VARCHAR2(64);
alter table CUBE_JOB_DETAIL modify UPDATE_BY VARCHAR2(64);
alter table CUBE_DIM_RULE_INDICATOR_OPERATION modify UPDATE_BY VARCHAR2(64);
alter table CUBE_SYS_NOTICE modify UPDATE_BY VARCHAR2(64);
alter table CUBE_SYS_MENU modify UPDATE_BY VARCHAR2(64);
alter table CUBE_DIM_RULE modify UPDATE_BY VARCHAR2(64);
alter table CUBE_SYS_DICT_TYPE modify UPDATE_BY VARCHAR2(64);
alter table CUBE_SYS_DICT_DATA modify UPDATE_BY VARCHAR2(64);
alter table CUBE_SYS_DEPT modify UPDATE_BY VARCHAR2(64);
alter table CUBE_DASHBOARD modify UPDATE_BY VARCHAR2(64);
alter table CUBE_SYS_CONFIG modify UPDATE_BY VARCHAR2(64);
alter table CUBE_WIDGET modify UPDATE_BY VARCHAR2(64);
alter table CUBE_GROUP_INSTANCE modify UPDATE_BY VARCHAR2(64);
alter table CUBE_WF_VARIABLE modify UPDATE_BY VARCHAR2(64);
alter table CUBE_SOURCE_DETAIL modify UPDATE_BY VARCHAR2(64);
alter table CUBE_SOURCE modify UPDATE_BY VARCHAR2(64);
alter table CUBE_WF_TASK modify UPDATE_BY VARCHAR2(64);
alter table CUBE_GROUP modify UPDATE_BY VARCHAR2(64);
alter table CUBE_WF_INSTANCE modify UPDATE_BY VARCHAR2(64);
alter table CUBE_WF_HISTORY modify UPDATE_BY VARCHAR2(64);
alter table CUBE_DIM_LAYOUT modify USER_ID VARCHAR2(32);
alter table CUBE_WF_TASK modify USER_ID VARCHAR2(64);
alter table CUBE_WF_HISTORY modify USER_ID VARCHAR2(64);
alter table CUBE_SYS_USER modify USER_NAME VARCHAR2(30);
alter table CUBE_SYS_LOGININFOR modify USER_NAME VARCHAR2(50);
alter table CUBE_WF_TASK modify USER_NAME VARCHAR2(128);
alter table CUBE_WF_HISTORY modify USER_NAME VARCHAR2(128);
alter table CUBE_SYS_USER modify USER_TYPE VARCHAR2(2);
alter table CUBE_WF_VARIABLE modify VARIABLE_KEY VARCHAR2(200);
alter table CUBE_WF_VARIABLE modify VARIABLE_VAL VARCHAR2(200);
alter table CUBE_TABLE_VERSION modify VERSION VARCHAR2(30);
alter table CUBE_DIM_TABLE modify VIEW_ID VARCHAR2(32);
alter table CUBE_2D_TABLE modify VIEW_ID VARCHAR2(32);
alter table CUBE_WIDGET modify VIEW_ID VARCHAR2(32);
alter table CUBE_VIEW modify VIEW_NAME VARCHAR2(64);
alter table CUBE_WIDGET modify VIEW_NAME VARCHAR2(64);

COMMENT
ON COLUMN CUBE_SOURCE_DETAIL.TABLE_META IS '表格元数据';
COMMENT
ON COLUMN CUBE_JOB_DETAIL.TABLE_NAME IS '表格名称';
COMMENT
ON COLUMN CUBE_SOURCE_DETAIL.TABLE_NAME IS '表格名称';
COMMENT
ON COLUMN CUBE_WF_TASK.TABLE_NAME IS '表格名称';
COMMENT
ON COLUMN CUBE_WF_INSTANCE.TABLE_NAME IS '表格名称';

COMMENT
ON COLUMN CUBE_JOB.UPDATE_BY IS '更新人';
COMMENT
ON COLUMN CUBE_SYS_DICT_TYPE.UPDATE_BY IS '更新人';
COMMENT
ON COLUMN CUBE_SYS_USER.UPDATE_BY IS '更新人';
COMMENT
ON COLUMN CUBE_SYS_MENU.UPDATE_BY IS '更新人';
COMMENT
ON COLUMN CUBE_SYS_NOTICE.UPDATE_BY IS '更新人';
COMMENT
ON COLUMN CUBE_SYS_DEPT.UPDATE_BY IS '更新人';
COMMENT
ON COLUMN CUBE_JOB_DETAIL.UPDATE_BY IS '更新人';
COMMENT
ON COLUMN CUBE_SYS_CONFIG.UPDATE_BY IS '更新人';
COMMENT
ON COLUMN CUBE_SYS_DICT_DATA.UPDATE_BY IS '更新人';
COMMENT
ON COLUMN CUBE_SYS_ROLE.UPDATE_BY IS '更新人';
COMMENT
ON COLUMN CUBE_SYS_POST.UPDATE_BY IS '更新人';


alter table CUBE_WF_TASK modify USER_ID VARCHAR2(32);
alter table CUBE_WF_HISTORY modify USER_ID VARCHAR2(32);
COMMENT
ON COLUMN CUBE_WF_TASK.USER_ID IS '用户ID';
COMMENT
ON COLUMN CUBE_WF_HISTORY.USER_ID IS '用户ID';
COMMENT
ON COLUMN CUBE_WF_DIM_DEFINE_DETAIL.USER_ID IS '用户ID';


alter table CUBE_SYS_USER modify USER_NAME VARCHAR2(128);
alter table CUBE_SYS_LOGININFOR modify USER_NAME VARCHAR2(128);
COMMENT
ON COLUMN CUBE_WF_TASK.USER_NAME IS '用户账号';
COMMENT
ON COLUMN CUBE_WF_HISTORY.USER_NAME IS '用户账号';


COMMENT
ON COLUMN CUBE_WIDGET.VIEW_ID IS '数据视图ID';

alter table CUBE_TABLE_VERSION modify VERSION VARCHAR2(200);
alter table CUBE_2D_TABLE_BACKUP_VERSION modify VERSION_NAME VARCHAR2(200);

COMMENT
ON COLUMN CUBE_WF_DIM_DEFINE_CENTRALIZE.USER_ID IS '用户ID';

COMMENT
ON COLUMN CUBE_DASHBOARD.ID IS '主键ID';
COMMENT
ON COLUMN CUBE_JOB_DETAIL.ID IS '主键ID';
COMMENT
ON COLUMN CUBE_WIDGET.ID IS '主键ID';
COMMENT
ON COLUMN CUBE_RULE_FUNC_GROUP.ID IS '主键ID';

alter table CUBE_SYS_LOGININFOR modify BROWSER VARCHAR2(2000);

COMMENT
ON COLUMN CUBE_SYS_LOGININFOR.BROWSER IS '浏览器信息';
alter table CUBE_SYS_LOGININFOR
    modify BROWSER VARCHAR2(2000);
COMMENT
ON COLUMN CUBE_SYS_LOGININFOR.BROWSER IS '浏览器信息';

alter table CUBE_2D_TABLE_BACKUP_CONFIG
    modify COLUMN_CODE VARCHAR2(64);
alter table CUBE_RULE
    modify COLUMN_CODE VARCHAR2(64);
COMMENT
ON COLUMN CUBE_2D_TABLE_BACKUP_CONFIG.COLUMN_CODE IS '字段编码';
COMMENT
ON COLUMN CUBE_RULE.COLUMN_CODE IS '字段编码';

alter table CUBE_2D_TABLE_BACKUP_CONFIG
    modify COLUMN_TYPE VARCHAR2(18);
COMMENT
ON COLUMN CUBE_2D_TABLE_BACKUP_CONFIG.COLUMN_TYPE IS '字段类型（varchar2,date,int）';

COMMENT
ON COLUMN CUBE_SYS_CONFIG.CONFIG_ID IS '配置id';


alter table CUBE_SYS_CONFIG
    modify CONFIG_VALUE VARCHAR2(240);
COMMENT
ON COLUMN CUBE_SYS_CONFIG.CONFIG_VALUE IS '配置值';

alter table CUBE_JOB
    modify CRON_EXPRESSION VARCHAR2(100);
COMMENT
ON COLUMN CUBE_JOB.CRON_EXPRESSION IS '定时任务表达式';

alter table CUBE_JOB_DETAIL
    modify DATA_VERSION VARCHAR2(20);

alter table CUBE_DIM_INSTANCE
    modify DIM_CODE VARCHAR2(50);
COMMENT
ON COLUMN CUBE_DIM_INSTANCE.DIM_CODE IS '维度编码';

alter table CUBE_DIM_RULE_INDICATOR_OPERATION
    modify DIM_DIRECTORY_NAME VARCHAR2(128);
COMMENT
ON COLUMN CUBE_DIM_RULE_INDICATOR_OPERATION.DIM_DIRECTORY_NAME IS '维度目录名称';

COMMENT
ON COLUMN CUBE_WF_DIM_DEFINE_DETAIL.DIM_ID IS '维度ID';

alter table CUBE_DIM_INSTANCE
    modify DIM_NAME VARCHAR2(250);
COMMENT
ON COLUMN CUBE_DIM_INSTANCE.DIM_NAME IS '维度名称';

alter table CUBE_SYS_OPER_LOG
    modify ERROR_MSG VARCHAR2(4000);

alter table CUBE_SOURCE_DETAIL
    modify FILE_NAME VARCHAR2(200);
alter table CUBE_SOURCE
    modify FILE_PATH VARCHAR2(200);

alter table CUBE_IND
    modify FUNCTION_NAME VARCHAR2(200);
COMMENT
ON COLUMN CUBE_IND.FUNCTION_NAME IS '功能名称';

alter table CUBE_GROUP
    modify GROUP_CODE VARCHAR2(120);
alter table CUBE_GROUP
    modify GROUP_NAME VARCHAR2(128);
alter table CUBE_RULE_FUNC_GROUP
    modify GROUP_NAME VARCHAR2(128);

alter table CUBE_SYS_MENU
    modify ICON VARCHAR2(128);
COMMENT
ON COLUMN CUBE_SYS_MENU.ICON IS '图标';

COMMENT
ON COLUMN CUBE_PERMISSION_DIM_INSTANCE.INSTANCE_ID IS '实例id';
COMMENT
ON COLUMN CUBE_WF_VARIABLE.INSTANCE_ID IS '实例id';
COMMENT
ON COLUMN CUBE_WF_TASK.INSTANCE_ID IS '实例id';
COMMENT
ON COLUMN CUBE_WF_HISTORY.INSTANCE_ID IS '实例id';

alter table CUBE_JOB_LOG
    modify JOB_GROUP VARCHAR2(200);
COMMENT
ON COLUMN CUBE_JOB_LOG.JOB_GROUP IS 'job组';
alter table CUBE_JOB
    modify JOB_GROUP VARCHAR2(200);
COMMENT
ON COLUMN CUBE_JOB.JOB_GROUP IS 'job组';

COMMENT
ON COLUMN CUBE_JOB_LOG.JOB_ID IS 'job id';
COMMENT
ON COLUMN CUBE_JOB_DETAIL.JOB_ID IS 'job id';
COMMENT
ON COLUMN CUBE_JOB.JOB_ID IS 'job id';

alter table CUBE_JOB_LOG
    modify JOB_NAME VARCHAR2(200);
COMMENT
ON COLUMN CUBE_JOB_LOG.JOB_NAME IS 'job名称';
alter table CUBE_JOB
    modify JOB_NAME VARCHAR2(200);
COMMENT
ON COLUMN CUBE_JOB.JOB_NAME IS 'job名称';

COMMENT
ON COLUMN CUBE_DIM_LAYOUT_FILTER.LAYOUT_ID IS '布局ID';

COMMENT
ON COLUMN CUBE_SYS_USER.LOGIN_DATE IS '登录时间';

COMMENT
ON COLUMN CUBE_SYS_ROLE_MENU.MENU_ID IS '菜单id';
COMMENT
ON COLUMN CUBE_SYS_MENU.MENU_ID IS '菜单id';

COMMENT
ON COLUMN CUBE_SYS_LOGININFOR.MSG IS '消息';

alter table CUBE_DASHBOARD
    modify NAME VARCHAR2(100);
alter table CUBE_WIDGET
    modify NAME VARCHAR2(100);

COMMENT
ON COLUMN CUBE_WF_TASK.NODE_ID IS '节点id';
COMMENT
ON COLUMN CUBE_WF_INSTANCE.NODE_ID IS '节点id';
COMMENT
ON COLUMN CUBE_WF_HISTORY.NODE_ID IS '节点id';
COMMENT
ON COLUMN CUBE_WF_VARIABLE.NODE_ID IS '节点id';


alter table CUBE_WF_TASK
    modify NODE_NAME VARCHAR2(100);
COMMENT
ON COLUMN CUBE_WF_TASK.NODE_NAME IS '节点名称';
alter table CUBE_WF_INSTANCE
    modify NODE_NAME VARCHAR2(100);
COMMENT
ON COLUMN CUBE_WF_INSTANCE.NODE_NAME IS '节点名称';
alter table CUBE_WF_HISTORY
    modify NODE_NAME VARCHAR2(100);
COMMENT
ON COLUMN CUBE_WF_HISTORY.NODE_NAME IS '节点名称';


COMMENT
ON COLUMN CUBE_JOB_DETAIL.PROCESS_NAME IS '流程名称';

alter table CUBE_SYS_USER
    modify REMARK VARCHAR2(250);
alter table CUBE_SYS_ROLE
    modify REMARK VARCHAR2(250);
alter table CUBE_SYS_POST
    modify REMARK VARCHAR2(250);
alter table CUBE_JOB
    modify REMARK VARCHAR2(250);
alter table CUBE_SYS_NOTICE
    modify REMARK VARCHAR2(250);
alter table CUBE_SYS_MENU
    modify REMARK VARCHAR2(250);
alter table CUBE_SYS_DICT_TYPE
    modify REMARK VARCHAR2(250);
alter table CUBE_SYS_DICT_DATA
    modify REMARK VARCHAR2(250);
alter table CUBE_SYS_CONFIG
    modify REMARK VARCHAR2(250);


alter table CUBE_SYS_OPER_LOG
    modify REQUEST_METHOD VARCHAR2(50);

alter table CUBE_SYS_ROLE
    modify ROLE_NAME VARCHAR2(256);

COMMENT
ON COLUMN CUBE_RULE.RULE_EXPRESS IS '规则表达式';

alter table CUBE_DIM_RULE
    modify RULE_NAME VARCHAR2(200);

COMMENT
ON COLUMN CUBE_WIDGET.SEQ IS '序列';

alter table CUBE_JOB_DETAIL
    modify TABLE_NAME VARCHAR2(50);
COMMENT
ON COLUMN CUBE_JOB_DETAIL.TABLE_NAME IS '表名';
alter table CUBE_DIM_TABLE
    modify TABLE_NAME VARCHAR2(50);
COMMENT
ON COLUMN CUBE_DIM_TABLE.TABLE_NAME IS '表名';
alter table CUBE_SOURCE_DETAIL
    modify TABLE_NAME VARCHAR2(50);
COMMENT
ON COLUMN CUBE_SOURCE_DETAIL.TABLE_NAME IS '表名';
alter table CUBE_WF_TASK
    modify TABLE_NAME VARCHAR2(50);
COMMENT
ON COLUMN CUBE_WF_TASK.TABLE_NAME IS '表名';
alter table CUBE_2D_TABLE
    modify TABLE_NAME VARCHAR2(50);
COMMENT
ON COLUMN CUBE_2D_TABLE.TABLE_NAME IS '表名';
alter table CUBE_WF_INSTANCE
    modify TABLE_NAME VARCHAR2(50);
COMMENT
ON COLUMN CUBE_WF_INSTANCE.TABLE_NAME IS '表名';


alter table CUBE_SYS_USER
    modify USER_NAME VARCHAR2(256);
COMMENT
ON COLUMN CUBE_SYS_USER.USER_NAME IS '用户名';
alter table CUBE_SYS_LOGININFOR
    modify USER_NAME VARCHAR2(256);
COMMENT
ON COLUMN CUBE_SYS_LOGININFOR.USER_NAME IS '用户名';
alter table CUBE_WF_TASK
    modify USER_NAME VARCHAR2(256);
COMMENT
ON COLUMN CUBE_WF_TASK.USER_NAME IS '用户名';
alter table CUBE_WF_HISTORY
    modify USER_NAME VARCHAR2(256);
COMMENT
ON COLUMN CUBE_WF_HISTORY.USER_NAME IS '用户名';

COMMENT
ON COLUMN CUBE_PERMISSION_DIM_INSTANCE.TABLE_ID IS '表ID';
COMMENT
ON COLUMN CUBE_WF_DEFINE.TABLE_ID IS '表ID';
COMMENT
ON COLUMN CUBE_PERMISSION_DIM.TABLE_ID IS '表ID';
COMMENT
ON COLUMN CUBE_PERMISSION.TABLE_ID IS '表ID';
COMMENT
ON COLUMN CUBE_2D_TABLE_BACKUP_CONFIG.TABLE_ID IS '表ID';
COMMENT
ON COLUMN CUBE_TABLE_VERSION.TABLE_ID IS '表ID';
COMMENT
ON COLUMN CUBE_JOB_DETAIL.TABLE_ID IS '表ID';
COMMENT
ON COLUMN CUBE_2D_TABLE_GEN.TABLE_ID IS '表ID';
COMMENT
ON COLUMN CUBE_DIM_TABLE_REL.TABLE_ID IS '表ID';
COMMENT
ON COLUMN CUBE_2D_TABLE_BACKUP_VERSION.TABLE_ID IS '表ID';
COMMENT
ON COLUMN CUBE_DIM_DIM_REL.TABLE_ID IS '表ID';
COMMENT
ON COLUMN CUBE_WF_TASK.TABLE_ID IS '表ID';
COMMENT
ON COLUMN CUBE_WF_INSTANCE.TABLE_ID IS '表ID';
COMMENT
ON COLUMN CUBE_WF_HISTORY.TABLE_ID IS '表ID';
COMMENT
ON COLUMN CUBE_RULE.TABLE_ID IS '表ID';
COMMENT
ON COLUMN CUBE_DIM_LAYOUT.TABLE_ID IS '表ID';

alter table CUBE_SYS_OPER_LOG modify BUSINESS_TYPE tinyint;
alter table CUBE_SYS_OPER_LOG modify OPERATOR_TYPE tinyint;

alter table CUBE_2D_TABLE modify CREATE_MODE VARCHAR2(11);
COMMENT
ON COLUMN CUBE_2D_TABLE.CREATE_MODE IS '建表方式（AUTO：自动；MANUAL：手动；AGGREGATION：存量表格聚合生成列）';
alter table CUBE_DIM_TABLE modify CREATE_MODE VARCHAR2(11);
COMMENT
ON COLUMN CUBE_DIM_TABLE.CREATE_MODE IS '建表方式（AUTO：自动；MANUAL：手动；AGGREGATION：存量表格聚合生成列）';

alter table CUBE_GROUP_INSTANCE modify DIC_FLAG CHAR (1);
COMMENT
ON COLUMN CUBE_GROUP_INSTANCE.DIC_FLAG IS '是否字典（N否Y是）';

alter table CUBE_DIM_INSTANCE modify IS_LEAF CHAR (1);
COMMENT
ON COLUMN CUBE_DIM_INSTANCE.IS_LEAF IS '是否叶子（N否Y是）';

COMMENT
ON COLUMN CUBE_2D_TABLE_BACKUP_VERSION.IS_OVERWRITE IS '是否自动覆盖同名备份（Y是；N否）';

COMMENT
ON COLUMN CUBE_SYS_ROLE.DEPT_CHECK_STRICTLY IS '部门树选择项是否关联显示（0：父子不互相关联显示 1：父子互相关联显示 ）';
COMMENT
ON COLUMN CUBE_SYS_ROLE.MENU_CHECK_STRICTLY IS '菜单树选择项是否关联显示（ 0：父子不互相关联显示 1：父子互相关联显示）';

COMMENT
ON COLUMN CUBE_WF_TASK.TASK_DONE IS '是否完成（ 0：否 1：是）';

COMMENT
ON COLUMN CUBE_2D_TABLE_BACKUP_VERSION.SUFFIX_TYPE IS '后缀类型（ 0：无后缀 1：年 2: 年月 3: 年月日）';

COMMENT
ON COLUMN CUBE_DIM_RULE_INDICATOR_REF.REF_TABLE_TYPE IS '引用表类型（grid：二维，cube：多维）';
COMMENT
ON COLUMN CUBE_DIM_RULE_INDICATOR_REF.REF_TYPE IS '引用类型（ind：指标，dim：维度）';

COMMENT
ON COLUMN CUBE_WF_HISTORY.OPERATE_TYPE IS '任务操作类型（0：提交，1：同意，2：撤回，3：退回）';

alter table CUBE_IND modify IND_TYPE VARCHAR2(8);
COMMENT
ON COLUMN CUBE_IND.IND_TYPE IS '指标类型（GROUP：分组，INSTANCE：指标实例）';

alter table CUBE_SYS_DEPT modify DEPT_TYPE VARCHAR2(6);
COMMENT
ON COLUMN CUBE_SYS_DEPT.DEPT_TYPE IS '机构属性（front：前台，middle：中台,back:后台）';

alter table CUBE_WF_DEFINE modify TYPE CHAR (1);
COMMENT
ON COLUMN CUBE_WF_DEFINE.TYPE IS '类型（0：分组，1：流程）';

alter table CUBE_WF_DEFINE modify FLOW_TYPE CHAR (1);
COMMENT
ON COLUMN CUBE_WF_DEFINE.FLOW_TYPE IS '流程实例类型（0：二维，1：多维）';

COMMENT
ON COLUMN CUBE_WF_TASK.FLOW_TYPE IS '流程实例类型（0：二维，1：多维）';
COMMENT
ON COLUMN CUBE_WF_INSTANCE.FLOW_TYPE IS '流程实例类型（0：二维，1：多维）';

COMMENT
ON COLUMN CUBE_RULE.RULE_TYPE IS '规则类型（1：规则定义内容；2:规则引用关系）';


ALTER TABLE CUBE_2D_TABLE_BACKUP_CONFIG
    ADD COLUMN REMARK varchar2(250);
comment
ON COLUMN CUBE_2D_TABLE_BACKUP_CONFIG.REMARK IS '备注';


ALTER TABLE CUBE_JOB_DETAIL
    ADD COLUMN SUFFIX_TYPE char(1);
comment
ON COLUMN CUBE_JOB_DETAIL.SUFFIX_TYPE IS '后缀类型（ 0：无后缀 1：年 2: 年月 3: 年月日）';
ALTER TABLE CUBE_JOB_DETAIL
    ADD COLUMN IS_OVERWRITE char(1);
comment
ON COLUMN CUBE_JOB_DETAIL.IS_OVERWRITE IS '是否自动覆盖同名备份（Y是；N否）';
