INSERT INTO cube_group_instance (id, group_id, group_instance_name, group_instance_desc, dic_flag,
                                 storage_type, operational_rule, decimal_places, show_format,
                                 prefix_char, suffix_char, raw_sample, show_sample, content_align,
                                 status, create_by, create_time, update_by, update_time)
VALUES ('b051c25ea4aee6f3f002e7e13ad19573', '1b3f4ace5a71f2d6e85d68eb68e68b53', '日期时间', NULL, 'N', 'DATE', '', 0,
        '14', '', '', '1900/1/1', '', 'left', '0', NULL, '2025-05-21 10:48:48', 'admin', '2025-05-21 14:16:56');
INSERT INTO cube_rule_func (id, func_name, func_val, func_desc, func_group, func_seq, alias, func_scope)
VALUES (60, '日期相减（精确）', 'dateSubDateEX(,)',
        'dateSubDateEX(【参数1】,【参数2】)；说明：返回【参数1】和【参数2】之间相差的天数，精确到秒。', 4, 17, 'dateSubDateEX',
        '1,2');
INSERT INTO cube_rule_func (id, func_name, func_val, func_desc, func_group, func_seq, alias, func_scope)
VALUES (61, '判断日期是否季度末', 'isQuarterEnd()', 'isQuarterEnd(【参数1】)；说明：判断【参数1】是否季度末。', 4, 18,
        'isQuarterEnd', '1,2');
commit;