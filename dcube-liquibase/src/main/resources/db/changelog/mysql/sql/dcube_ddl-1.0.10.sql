ALTER TABLE cube_model_scene_setting
    ADD COLUMN df_input_example text NULL COMMENT '底座表输入示例';

ALTER TABLE cube_2d_table
    DROP COLUMN memSize;

ALTER TABLE cube_permission
    MODIFY COLUMN role_id int NULL DEFAULT NULL COMMENT '角色ID';
ALTER TABLE cube_permission
    MODIFY COLUMN table_id int NULL DEFAULT NULL COMMENT '二维表ID';

ALTER TABLE cube_dim_instance
    MODIFY COLUMN dim_directory_id int NULL DEFAULT NULL COMMENT '维度目录ID';

create unique index cube_2d_table_backup_config_table_id_column_code_unique_index
    on cube_2d_table_backup_config (table_id, column_code);

-- ----------------------------
-- Table structure for cube_dim_rule_variable
-- ----------------------------
DROP TABLE IF EXISTS cube_dim_rule_variable;
CREATE TABLE cube_dim_rule_variable
(
    id            BIGINT       NOT NULL COMMENT '主键ID',
    dim_table_id  BIGINT       NULL COMMENT '多维表ID',
    dim_rule_id   BIGINT       NULL COMMENT '多维表规则ID',
    variable_name VARCHAR(100) NOT NULL COMMENT '变量名称',
    create_by     VARCHAR(64)  NULL COMMENT '创建人',
    create_time   DATETIME     NULL COMMENT '创建时间',
    update_by     VARCHAR(64)  NULL COMMENT '更新人',
    update_time   DATETIME     NULL COMMENT '更新时间',
    del_flag      CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
    COMMENT
        '多维计算规则多维变量';

ALTER TABLE cube_dim_rule_variable
    ADD COLUMN variable_type tinyint DEFAULT 0 COMMENT '变量类型（1目标变量，0自定义变量）';

-- ----------------------------
-- Table structure for cube_dim_rule_variable_detail
-- ----------------------------
DROP TABLE IF EXISTS cube_dim_rule_variable_detail;
CREATE TABLE cube_dim_rule_variable_detail
(
    id                         BIGINT      NOT NULL COMMENT '主键ID',
    dim_rule_variable_id       BIGINT      NOT NULL COMMENT '多维变量ID',
    dim_rule_variable_table_id BIGINT      NULL COMMENT '变量多维表ID',
    dim_type                   tinyint     NOT NULL COMMENT '维度类型（0维度，1指标）',
    dim_directory_id           BIGINT      NULL COMMENT '维度ID',
    effect_scope               text        NULL COMMENT '生效范围',
    effect_scope_size          int         NULL COMMENT '生效范围数量',
    create_by                  VARCHAR(64) NULL COMMENT '创建人',
    create_time                DATETIME    NULL COMMENT '创建时间',
    update_by                  VARCHAR(64) NULL COMMENT '更新人',
    update_time                DATETIME    NULL COMMENT '更新时间',
    del_flag                   CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
    COMMENT
        '多维计算规则多维变量详情';

ALTER TABLE cube_dim_rule_variable_detail
    ADD INDEX cube_dim_rule_variable_detail_variable_id_index (dim_rule_variable_id, del_flag);

-- ----------------------------
-- Table structure for cube_dim_rule_dim_operation
-- ----------------------------
DROP TABLE IF EXISTS cube_dim_rule_dim_operation;
CREATE TABLE cube_dim_rule_dim_operation
(
    id              BIGINT      NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    dim_table_id    BIGINT      NULL COMMENT '多维表ID',
    dim_rule_id     BIGINT      NULL COMMENT '多维表规则ID',
    rule_expression text        NULL COMMENT '规则表达式',
    create_by       VARCHAR(64) NULL COMMENT '创建人',
    create_time     DATETIME    NULL COMMENT '创建时间',
    update_by       VARCHAR(64) NULL COMMENT '更新人',
    update_time     DATETIME    NULL COMMENT '更新时间',
    del_flag        CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
    COMMENT
        '多维计算规则维度运算';


ALTER TABLE cube_dim_rule_dim_operation
    ADD INDEX cube_dim_rule_dim_operation_rule_id_index (dim_rule_id, del_flag);

ALTER table cube_dim_rule_dim_operation
    add column order_num int(11) DEFAULT '0' COMMENT '显示顺序';