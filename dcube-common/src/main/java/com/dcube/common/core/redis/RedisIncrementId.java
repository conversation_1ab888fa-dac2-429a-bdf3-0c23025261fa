package com.dcube.common.core.redis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @创建人 zhouhx
 * @创建时间 2023/8/11 14:16
 * @描述
 */
@Component
public class RedisIncrementId {

    @Autowired
    public RedisTemplate redisTemplate;

    public Integer createIncreId(String key,Integer currentId){
        //查询 key 是否存在， 不存在返回 1 ，存在的话则自增加1
        Object id = redisTemplate.opsForValue().get(key);
        if(Objects.nonNull(id)){
            redisTemplate.opsForValue().set(key,id);
        }else{
            redisTemplate.opsForValue().set(key,currentId);
        }
        return Math.toIntExact(redisTemplate.opsForValue().increment(key, 1));
    }
}
