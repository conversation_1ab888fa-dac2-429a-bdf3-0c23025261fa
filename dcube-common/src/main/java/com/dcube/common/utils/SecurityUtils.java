package com.dcube.common.utils;

import com.dcube.common.constant.HttpStatus;
import com.dcube.common.core.domain.model.LoginUser;
import com.dcube.common.exception.ServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.util.ArrayList;
import java.util.List;

/**
 * 安全服务工具类
 *
 * <AUTHOR>
 */
public class SecurityUtils {

    private static final String LOGIN_USER = "LOGIN_USER";
    // 定义允许的特殊字符集合
    private static final String ALLOWED_SPECIAL_CHARS = "!@#$%^&*()_+-=[]{}|;:'\",.<>/?";

    /**
     * 用户ID
     **/
    public static Long getUserId() {
        try {
            return getLoginUser().getUserId();
        } catch (Exception e) {
            throw new ServiceException("获取用户ID异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取部门ID
     **/
    public static Long getDeptId() {
        try {
            return getLoginUser().getDeptId();
        } catch (Exception e) {
            throw new ServiceException("获取部门ID异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取用户账户
     **/
    public static String getUsername() {
        try {
            return getLoginUser().getUsername();
        } catch (Exception e) {
            throw new ServiceException("获取用户账户异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取用户
     **/
    public static LoginUser getLoginUser() {
        LoginUser loginUser = ThreadLocalUtils.getTtl(SecurityUtils.class, LOGIN_USER);
        if (loginUser != null) {
            return loginUser;
        }
        try {
            return (LoginUser) getAuthentication().getPrincipal();
        } catch (Exception e) {
            throw new ServiceException("获取用户信息异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 设置用户
     **/
    public static void setLoginUser() {
        ThreadLocalUtils.setTtl(SecurityUtils.class, LOGIN_USER, getLoginUser());
    }

    /**
     * 设置用户
     **/
    public static void setLoginUser(LoginUser loginUser) {
        ThreadLocalUtils.setTtl(SecurityUtils.class, LOGIN_USER, loginUser);
    }

    /**
     * 移除用户
     **/
    public static void remove() {
        ThreadLocalUtils.removeTtl(SecurityUtils.class, LOGIN_USER);
    }


    /**
     * 获取Authentication
     */
    public static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 判断密码是否相同
     *
     * @param rawPassword 真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * 是否为管理员
     *
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(Long userId) {
        return userId != null && 1L == userId;
    }

    /**
     * 校验密码是否合法，规则如下：
     * 1. 密码不能为空
     * 2. 长度为6~18位
     * 3. 不允许包含空格
     * 4. 仅允许以下四类字符：
     *    - 大写字母 A-Z
     *    - 小写字母 a-z
     *    - 数字 0-9
     *    - 特殊字符（来自允许列表）
     * 5. 至少包含上述字符类型中的任意两种组合
     *
     * @param password 待校验的密码字符串
     * @throws IllegalArgumentException 如果密码不符合规则
     */
    public static void validatePassword(String password) {

        // 1. 非空校验
        if (password == null) {
            throw new IllegalArgumentException("密码不能为空");
        }

        // 2. 长度校验
        int length = password.length();
        if (length < 6) {
            throw new IllegalArgumentException("密码长度必须至少6个字符，当前长度: " + length);
        }
        if (length > 18) {
            throw new IllegalArgumentException("密码长度最长18个字符，当前长度: " + length);
        }

        // 3. 空格校验
        if (password.contains(" ")) {
            throw new IllegalArgumentException("密码不能包含空格");
        }

        // 4. 初始化字符类型标志
        boolean hasUpper = false;
        boolean hasLower = false;
        boolean hasDigit = false;
        boolean hasSpecial = false;

        // 5. 遍历密码每个字符，判断合法性和类型
        for (int i = 0; i < password.length(); i++) {
            char c = password.charAt(i);
            if (Character.isUpperCase(c)) {
                hasUpper = true;
            } else if (Character.isLowerCase(c)) {
                hasLower = true;
            } else if (Character.isDigit(c)) {
                hasDigit = true;
            } else if (ALLOWED_SPECIAL_CHARS.indexOf(c) != -1) {
                hasSpecial = true;
            } else {
                // 出现非法字符，抛出异常并标明位置与 Unicode 编码
                throw new IllegalArgumentException(
                        String.format(
                                "密码包含非法字符 '%s'（位置: %d, Unicode: \\u%04X）。\n只允许以下字符类型: 大写字母(A-Z)、小写字母(a-z)、数字(0-9)、特殊字符(%s)",
                                c, i + 1, (int) c, ALLOWED_SPECIAL_CHARS
                        )
                );
            }
        }

        // 6. 统计实际包含的字符类型数量
        int typeCount = 0;
        List<String> presentTypes = new ArrayList<>();
        if (hasUpper) {
            typeCount++;
            presentTypes.add("大写字母");
        }
        if (hasLower) {
            typeCount++;
            presentTypes.add("小写字母");
        }
        if (hasDigit) {
            typeCount++;
            presentTypes.add("数字");
        }
        if (hasSpecial) {
            typeCount++;
            presentTypes.add("特殊字符");
        }

        // 7. 类型组合校验
        if (typeCount < 2) {
            throw new IllegalArgumentException(
                    "密码必须包含以下字符类型中至少两种组合: [大写字母, 小写字母, 数字, 特殊字符]。\n" +
                            "当前包含: " + presentTypes + " (" + typeCount + "种类型)\n" +
                            "允许的特殊字符: " + ALLOWED_SPECIAL_CHARS
            );
        }
    }
}
